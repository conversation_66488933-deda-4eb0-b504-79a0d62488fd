/*
 * Copyright (c) 2024, ArtInChip Technology Co., Ltd
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * QR Code Configuration - Unified Configuration File
 * This file contains all QR code related configurations.
 * All other QR code files should include this header to avoid duplication.
 * Authors: <AUTHORS>
 */

#ifndef __QRCODE_CONFIG_H__
#define __QRCODE_CONFIG_H__

/* QR Code Data Manager Configuration */
#define QRCODE_DATA_MANAGER_ENABLE  1

/* Buffer sizes */
#ifndef QRCODE_MAX_STRING_LEN
#define QRCODE_MAX_STRING_LEN       128
#endif

#ifndef QRCODE_JSON_BUFFER_SIZE
#define QRCODE_JSON_BUFFER_SIZE     512
#endif

/* Storage configuration */
#ifndef QRCODE_DATA_SIZE
#define QRCODE_DATA_SIZE            (4 * 1024)  /* 4KB */
#endif

#ifndef QRCODE_MTD_DEVICE_NAME
#define QRCODE_MTD_DEVICE_NAME      "norflash0"
#endif

#ifndef QRCODE_DATA_OFFSET
#define QRCODE_DATA_OFFSET          (640 * 1024)  /* 640KB offset */
#endif

/* Debug configuration */
#ifndef QRCODE_DEBUG_ENABLE
#define QRCODE_DEBUG_ENABLE         1
#endif

/* Predefined key names */
#define QRCODE_KEY_DEVICE_ID        "device_id"
#define QRCODE_KEY_DEVICE_SN        "device_sn"
#define QRCODE_KEY_WIFI_SSID        "wifi_ssid"
#define QRCODE_KEY_WIFI_PASSWORD    "wifi_password"
#define QRCODE_KEY_SERVER_URL       "server_url"
#define QRCODE_KEY_CUSTOM_INFO      "custom_info"
#define QRCODE_KEY_MODEL            "model"
#define QRCODE_KEY_VERSION          "version"

/* Auto-initialization */
#ifndef QRCODE_AUTO_INIT
#define QRCODE_AUTO_INIT            1
#endif

/* UI integration */
#ifndef QRCODE_UI_AUTO_REFRESH
#define QRCODE_UI_AUTO_REFRESH      1
#endif

#endif /* __QRCODE_CONFIG_H__ */