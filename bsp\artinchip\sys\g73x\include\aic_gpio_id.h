/*
 * Copyright (c) 2022, Artinchip Technology Co., Ltd
 *
 * SPDX-License-Identifier: Apache-2.0
 */

#ifndef __AIC_GPIO_ID_H__
#define __AIC_GPIO_ID_H__

#ifdef __cplusplus
extern "C" {
#endif

enum {
    PA_GROUP,
    PB_GROUP,
    PC_GROUP,
    PD_GROUP,
    PE_GROUP,
    PF_GROUP,
    PG_GROUP,
    PO_GROUP = 14,
    GPIO_GROUP_MAX,
};

#define PA_BASE  0
#define PB_BASE  32
#define PC_BASE  64
#define PD_BASE  96
#define PE_BASE  128
#define PF_BASE  160
#define PG_BASE  192
#define PH_BASE  224
#define PI_BASE  256
#define PJ_BASE  288
#define PK_BASE  320
#define PL_BASE  352
#define PM_BASE  384
#define PN_BASE  416
#define PO_BASE  448
#define GPIOA(n) (PA_BASE + (n))
#define GPIOB(n) (PB_BASE + (n))
#define GPIOC(n) (PC_BASE + (n))
#define GPIOD(n) (PD_BASE + (n))
#define GPIOE(n) (PE_BASE + (n))
#define GPIOF(n) (PF_BASE + (n))
#define GPIOG(n) (PG_BASE + (n))
#define GPIOH(n) (PH_BASE + (n))
#define GPIOI(n) (PI_BASE + (n))
#define GPIOJ(n) (PJ_BASE + (n))
#define GPIOK(n) (PK_BASE + (n))
#define GPIOL(n) (PL_BASE + (n))
#define GPIOM(n) (PM_BASE + (n))
#define GPION(n) (PN_BASE + (n))
#define GPIOO(n) (PO_BASE + (n))

typedef enum {
    PA0  = GPIOA(0),
    PA1  = GPIOA(1),
    PA2  = GPIOA(2),
    PA3  = GPIOA(3),
    PA4  = GPIOA(4),
    PA5  = GPIOA(5),
    PA6  = GPIOA(6),
    PA7  = GPIOA(7),
    PA8  = GPIOA(8),
    PA9  = GPIOA(9),
    PA10 = GPIOA(10),
    PA11 = GPIOA(11),

    PB0  = GPIOB(0),
    PB1  = GPIOB(1),
    PB2  = GPIOB(2),
    PB3  = GPIOB(3),
    PB4  = GPIOB(4),
    PB5  = GPIOB(5),
    PB6  = GPIOB(6),
    PB7  = GPIOB(7),
    PB8  = GPIOB(8),
    PB9  = GPIOB(9),
    PB10 = GPIOB(10),
    PB11 = GPIOB(11),

    PC0 = GPIOC(0),
    PC1 = GPIOC(1),
    PC2 = GPIOC(2),
    PC3 = GPIOC(3),
    PC4 = GPIOC(4),
    PC5 = GPIOC(5),
    PC6 = GPIOC(6),
    PC7 = GPIOC(7),

    PD0  = GPIOD(0),
    PD1  = GPIOD(1),
    PD2  = GPIOD(2),
    PD3  = GPIOD(3),
    PD4  = GPIOD(4),
    PD5  = GPIOD(5),
    PD6  = GPIOD(6),
    PD7  = GPIOD(7),
    PD8  = GPIOD(8),
    PD9  = GPIOD(9),
    PD10 = GPIOD(10),
    PD11 = GPIOD(11),
    PD12 = GPIOD(12),
    PD13 = GPIOD(13),
    PD14 = GPIOD(14),
    PD15 = GPIOD(15),
    PD16 = GPIOD(16),
    PD17 = GPIOD(17),
    PD18 = GPIOD(18),
    PD19 = GPIOD(19),
    PD20 = GPIOD(20),
    PD21 = GPIOD(21),
    PD22 = GPIOD(22),
    PD23 = GPIOD(23),
    PD24 = GPIOD(24),
    PD25 = GPIOD(25),
    PD26 = GPIOD(26),
    PD27 = GPIOD(27),

    PE0  = GPIOE(0),
    PE1  = GPIOE(1),
    PE2  = GPIOE(2),
    PE3  = GPIOE(3),
    PE4  = GPIOE(4),
    PE5  = GPIOE(5),
    PE6  = GPIOE(6),
    PE7  = GPIOE(7),
    PE8  = GPIOE(8),
    PE9  = GPIOE(9),
    PE10 = GPIOE(10),
    PE11 = GPIOE(11),
    PE12 = GPIOE(12),
    PE13 = GPIOE(13),
    PE14 = GPIOE(14),
    PE15 = GPIOE(15),
    PE16 = GPIOE(16),
    PE17 = GPIOE(17),
    PE18 = GPIOE(18),
    PE19 = GPIOE(19),

    PF0  = GPIOF(0),
    PF1  = GPIOF(1),
    PF2  = GPIOF(2),
    PF3  = GPIOF(3),
    PF4  = GPIOF(4),
    PF5  = GPIOF(5),
    PF6  = GPIOF(6),
    PF7  = GPIOF(7),
    PF8  = GPIOF(8),
    PF9  = GPIOF(9),
    PF10 = GPIOF(10),
    PF11 = GPIOF(11),
    PF12 = GPIOF(12),
    PF13 = GPIOF(13),
    PF14 = GPIOF(14),
    PF15 = GPIOF(15),

    PG0 = GPIOG(0),
    PG1 = GPIOG(1),
    PG2 = GPIOG(2),
    PG3 = GPIOG(3),

    /* To aviod compile warnings. */
    GPIO_MAX_PIN,
} pin_name_t;

typedef enum {
    PA0_GPAI0        = 2U,
    PA0_PSADC0       = 3U,
    PA0_I2C0_SCK     = 4U,
    PA0_UART0_TX     = 5U,
    PA0_AMIC_IN      = 6U,
    PA0_IR_TX        = 7U,
    PA0_EPHY_LED0    = 8U,
    PA1_GPAI1        = 2U,
    PA1_PSADC1       = 3U,
    PA1_I2C0_SDA     = 4U,
    PA1_UART0_RX     = 5U,
    PA1_AMIC_BIAS    = 6U,
    PA1_IR_RX        = 7U,
    PA1_EPHY_LED1    = 8U,
    PA2_GPAI2        = 2U,
    PA2_PSADC2       = 3U,
    PA2_UART0_RTS    = 5U,
    PA3_GPAI3        = 2U,
    PA3_PSADC3       = 3U,
    PA3_UART0_CTS    = 5U,
    PA4_GPAI4        = 2U,
    PA4_PSADC4       = 3U,
    PA4_UART1_TX     = 5U,
    PA5_GPAI5        = 2U,
    PA5_PSADC5       = 3U,
    PA5_UART1_RX     = 5U,
    PA6_GPAI6        = 2U,
    PA6_PSADC6       = 3U,
    PA6_I2C1_SCK     = 4U,
    PA6_UART1_RTS    = 5U,
    PA7_GPAI7        = 2U,
    PA7_PSADC7       = 3U,
    PA7_I2C1_SDA     = 4U,
    PA7_UART1_CTS    = 5U,
    PA8_RTP_XP       = 2U,
    PA8_PSADC8       = 3U,
    PA8_I2C2_SCK     = 4U,
    PA8_UART2_TX     = 5U,
    PA8_JTAG_DO      = 6U,
    PA9_RTP_YP       = 2U,
    PA9_PSADC9       = 3U,
    PA9_I2C2_SDA     = 4U,
    PA9_UART2_RX     = 5U,
    PA9_JTAG_DI      = 6U,
    PA10_RTP_XN      = 2U,
    PA10_PSADC10     = 3U,
    PA10_I2C3_SCK    = 4U,
    PA10_UART2_RTS   = 5U,
    PA10_JTAG_MS     = 6U,
    PA11_RTP_YN      = 2U,
    PA11_PSADC11     = 3U,
    PA11_I2C3_SDA    = 4U,
    PA11_UART2_CTS   = 5U,
    PA11_JTAG_CK     = 6U,
    PA11_PSADC_TRIG  = 7U,
    PB0_SDC0_CMD     = 2U,
    PB0_SPI0_HOLD    = 3U,
    PB0_I2C1_SCK     = 4U,
    PB0_UART7_TX     = 5U,
    PB0_EPHY_LED0    = 8U,
    PB1_SDC0_CLK     = 2U,
    PB1_SPI0_WP      = 3U,
    PB1_I2C1_SDA     = 4U,
    PB1_UART7_RX     = 5U,
    PB1_EPHY_LED1    = 8U,
    PB2_SDC0_D3      = 2U,
    PB2_SPI0_CS      = 3U,
    PB3_SDC0_D0      = 2U,
    PB3_SPI0_MISO    = 3U,
    PB4_SDC0_D1      = 2U,
    PB4_SPI0_MOSI    = 3U,
    PB5_SDC0_D2      = 2U,
    PB5_SPI0_CLK     = 3U,
    PB6_SDC0_D4      = 2U,
    PB6_SPI1_HOLD    = 3U,
    PB6_I2C2_SCK     = 4U,
    PB6_UART4_TX     = 5U,
    PB6_CLK_OUT2     = 7U,
    PB6_CLK_OUT3     = 8U,
    PB7_SDC0_D5      = 2U,
    PB7_SPI1_WP      = 3U,
    PB7_I2C2_SDA     = 4U,
    PB7_UART4_RX     = 5U,
    PB8_SDC0_D6      = 2U,
    PB8_SPI1_CS      = 3U,
    PB8_UART4_RTS    = 4U,
    PB8_UART5_TX     = 5U,
    PB8_PSADC_TRIG   = 6U,
    PB8_IR_RX        = 7U,
    PB9_SDC0_D7      = 2U,
    PB9_SPI1_MISO    = 3U,
    PB9_UART6_RTS    = 4U,
    PB9_UART5_RX     = 5U,
    PB9_IR_TX        = 7U,
    PB10_SDC0_DS     = 2U,
    PB10_SPI1_MOSI   = 3U,
    PB10_UART6_TX    = 5U,
    PB11_SDC0_RST    = 2U,
    PB11_SPI1_CLK    = 3U,
    PB11_UART6_RX    = 5U,
    PC0_SDC1_D1      = 2U,
    PC0_LCD_D5       = 3U,
    PC0_SPI2_CLK     = 4U,
    PC0_UART1_TX     = 5U,
    PC0_JTAG_MS      = 6U,
    PC0_PWM0         = 7U,
    PC0_DBG_IO0      = 8U,
    PC1_SDC1_D0      = 2U,
    PC1_LCD_D4       = 3U,
    PC1_SPI2_CS      = 4U,
    PC1_UART1_RX     = 5U,
    PC1_JTAG_DI      = 6U,
    PC1_PWM1         = 7U,
    PC1_DBG_IO1      = 8U,
    PC2_SDC1_CLK     = 2U,
    PC2_LCD_D3       = 3U,
    PC2_SPI2_MOSI    = 4U,
    PC2_UART1_RTS    = 5U,
    PC2_UART0_TX     = 6U,
    PC2_PWM2         = 7U,
    PC2_DBG_IO2      = 8U,
    PC3_SDC1_CMD     = 2U,
    PC3_LCD_D2       = 3U,
    PC3_SPI2_MISO    = 4U,
    PC3_UART2_TX     = 5U,
    PC3_JTAG_DO      = 6U,
    PC3_PWM3         = 7U,
    PC3_DBG_IO3      = 8U,
    PC4_SDC1_D3      = 2U,
    PC4_LCD_D1       = 3U,
    PC4_UART2_RX     = 5U,
    PC4_UART0_RX     = 6U,
    PC4_PWM4         = 7U,
    PC4_DBG_IO4      = 8U,
    PC5_SDC1_D2      = 2U,
    PC5_LCD_D0       = 3U,
    PC5_UART2_RTS    = 4U,
    PC5_UART3_TX     = 5U,
    PC5_JTAG_CK      = 6U,
    PC5_PWM5         = 7U,
    PC5_DBG_IO5      = 8U,
    PC6_SDC1_DET     = 2U,
    PC6_CLK_OUT0     = 3U,
    PC6_DE_TE        = 4U,
    PC6_UART3_RX     = 5U,
    PC6_PWM6         = 7U,
    PC7_UART3_RTS    = 5U,
    PC7_PWM7         = 7U,
    PC7_EPHY_LEDIN1  = 8U,
    PD0_LCD_D0       = 2U,
    PD0_SPI2_CLK     = 3U,
    PD0_PBUS_AD0     = 6U,
    PD0_PWM0         = 7U,
    PD0_GMAC1_MDIO   = 8U,
    PD1_LCD_D1       = 2U,
    PD1_SPI2_CS      = 3U,
    PD1_PBUS_AD1     = 6U,
    PD1_PWM1         = 7U,
    PD1_EPHY_LEDIN0  = 8U,
    PD2_LCD_D2       = 2U,
    PD2_SPI2_MOSI    = 3U,
    PD2_DE_TE        = 4U,
    PD2_PBUS_AD2     = 6U,
    PD2_PWM2         = 7U,
    PD2_GMAC1_MDC    = 8U,
    PD3_LCD_D3       = 2U,
    PD3_SPI3_CLK     = 3U,
    PD3_PBUS_AD3     = 6U,
    PD3_PWM3         = 7U,
    PD3_GMAC1_TXCTL  = 8U,
    PD4_LCD_D4       = 2U,
    PD4_SPI3_CS      = 3U,
    PD4_PBUS_AD4     = 6U,
    PD4_PWM4         = 7U,
    PD5_LCD_D5       = 2U,
    PD5_SPI3_MOSI    = 3U,
    PD5_PBUS_AD5     = 6U,
    PD5_PWM5         = 7U,
    PD5_GMAC1_TXD1   = 8U,
    PD6_LCD_D6       = 2U,
    PD6_SPI3_MISO    = 3U,
    PD6_I2C0_SCK     = 4U,
    PD6_UART1_TX     = 5U,
    PD6_PBUS_AD6     = 6U,
    PD6_DBG_IO6      = 8U,
    PD7_LCD_D7       = 2U,
    PD7_SPI2_MISO    = 3U,
    PD7_I2C0_SDA     = 4U,
    PD7_UART1_RX     = 5U,
    PD7_PBUS_AD7     = 6U,
    PD7_DBG_IO7      = 8U,
    PD8_LCD_D8       = 2U,
    PD8_LVDS1_D0N    = 3U,
    PD8_SPI1_HOLD    = 4U,
    PD8_UART2_TX     = 5U,
    PD8_PBUS_AD8     = 6U,
    PD8_APWM0_A      = 7U,
    PD8_DBG_IO8      = 8U,
    PD9_LCD_D9       = 2U,
    PD9_LVDS1_D0P    = 3U,
    PD9_SPI1_WP      = 4U,
    PD9_UART2_RX     = 5U,
    PD9_PBUS_AD9     = 6U,
    PD9_APWM0_B      = 7U,
    PD9_DBG_IO9      = 8U,
    PD10_LCD_D10     = 2U,
    PD10_LVDS1_D1N   = 3U,
    PD10_SPI1_CS     = 4U,
    PD10_UART3_TX    = 5U,
    PD10_PBUS_AD10   = 6U,
    PD10_APWM1_A     = 7U,
    PD10_DBG_IO10    = 8U,
    PD11_LCD_D11     = 2U,
    PD11_LVDS1_D1P   = 3U,
    PD11_SPI1_MISO   = 4U,
    PD11_UART3_RX    = 5U,
    PD11_PBUS_AD11   = 6U,
    PD11_APWM1_B     = 7U,
    PD11_DBG_IO11    = 8U,
    PD12_LCD_D12     = 2U,
    PD12_LVDS1_D2N   = 3U,
    PD12_SPI1_MOSI   = 4U,
    PD12_UART4_TX    = 5U,
    PD12_PBUS_AD12   = 6U,
    PD12_APWM2_A     = 7U,
    PD12_DBG_IO12    = 8U,
    PD13_LCD_D13     = 2U,
    PD13_LVDS1_D2P   = 3U,
    PD13_SPI1_CLK    = 4U,
    PD13_UART4_RX    = 5U,
    PD13_PBUS_AD13   = 6U,
    PD13_APWM2_B     = 7U,
    PD13_DBG_IO13    = 8U,
    PD14_LCD_D14     = 2U,
    PD14_LVDS1_CKN   = 3U,
    PD14_SPI3_CLK    = 4U,
    PD14_CAP0        = 5U,
    PD14_PBUS_AD14   = 6U,
    PD14_QEP0_H0     = 7U,
    PD14_DBG_IO14    = 8U,
    PD15_LCD_D15     = 2U,
    PD15_LVDS1_CKP   = 3U,
    PD15_SPI3_CS     = 4U,
    PD15_CAP1        = 5U,
    PD15_PBUS_AD15   = 6U,
    PD15_QEP0_H1     = 7U,
    PD15_DBG_IO15    = 8U,
    PD16_LCD_D16     = 2U,
    PD16_LVDS1_D3N   = 3U,
    PD16_SPI3_MOSI   = 4U,
    PD16_CAP2        = 5U,
    PD16_PBUS_CLK    = 6U,
    PD16_QEP0_H2     = 7U,
    PD16_DBG_IO16    = 8U,
    PD17_LCD_D17     = 2U,
    PD17_LVDS1_D3P   = 3U,
    PD17_SPI3_MISO   = 4U,
    PD17_APWM_FLT5   = 5U,
    PD17_PBUS_NCS    = 6U,
    PD17_QEP0_A      = 7U,
    PD17_DBG_IO17    = 8U,
    PD18_LCD_D18     = 2U,
    PD18_LVDS0_D0N   = 3U,
    PD18_DSI_D0N     = 4U,
    PD18_I2C1_SCK    = 5U,
    PD18_PBUS_NADV   = 6U,
    PD18_QEP0_B      = 7U,
    PD18_DBG_IO18    = 8U,
    PD19_LCD_D19     = 2U,
    PD19_LVDS0_D0P   = 3U,
    PD19_DSI_D0P     = 4U,
    PD19_I2C1_SDA    = 5U,
    PD19_PBUS_NWE    = 6U,
    PD19_QEP0_I      = 7U,
    PD19_DBG_IO19    = 8U,
    PD20_LCD_D20     = 2U,
    PD20_LVDS0_D1N   = 3U,
    PD20_DSI_D1N     = 4U,
    PD20_UART7_TX    = 5U,
    PD20_PBUS_NOE    = 6U,
    PD20_QEP0_S      = 7U,
    PD20_DBG_IO20    = 8U,
    PD21_LCD_D21     = 2U,
    PD21_LVDS0_D1P   = 3U,
    PD21_DSI_D1P     = 4U,
    PD21_UART7_RX    = 5U,
    PD21_CLK_OUT0    = 6U,
    PD21_APWM_FLT0   = 7U,
    PD21_DBG_IO21    = 8U,
    PD22_LCD_D22     = 2U,
    PD22_LVDS0_D2N   = 3U,
    PD22_DSI_CKN     = 4U,
    PD22_I2C3_SCK    = 5U,
    PD22_UART6_TX    = 6U,
    PD22_APWM_FLT1   = 7U,
    PD22_DBG_IO22    = 8U,
    PD23_LCD_D23     = 2U,
    PD23_LVDS0_D2P   = 3U,
    PD23_DSI_CKP     = 4U,
    PD23_I2C3_SDA    = 5U,
    PD23_UART6_RX    = 6U,
    PD23_APWM_FLT2   = 7U,
    PD23_DBG_IO23    = 8U,
    PD24_LCD_C0      = 2U,
    PD24_LVDS0_CKN   = 3U,
    PD24_DSI_D2N     = 4U,
    PD24_UART5_TX    = 5U,
    PD24_SPI1_CLK    = 6U,
    PD24_APWM_FLT3   = 7U,
    PD24_DBG_IO24    = 8U,
    PD25_LCD_C1      = 2U,
    PD25_LVDS0_CKP   = 3U,
    PD25_DSI_D2P     = 4U,
    PD25_UART5_RX    = 5U,
    PD25_SPI1_CS     = 6U,
    PD25_APWM_FLT4   = 7U,
    PD25_DBG_IO25    = 8U,
    PD26_LCD_C2      = 2U,
    PD26_LVDS0_D3N   = 3U,
    PD26_DSI_D3N     = 4U,
    PD26_PWM6        = 5U,
    PD26_SPI1_MOSI   = 6U,
    PD26_APWM0_SI    = 7U,
    PD26_DBG_CLK     = 8U,
    PD27_LCD_C3      = 2U,
    PD27_LVDS0_D3P   = 3U,
    PD27_DSI_D3P     = 4U,
    PD27_PWM7        = 5U,
    PD27_SPI1_MISO   = 6U,
    PD27_APWM0_SO    = 7U,
    PD27_RTC_32K     = 8U,
    PE0_DVP_D0       = 3U,
    PE0_I2C0_SCK     = 4U,
    PE0_GMAC0_RXD1   = 6U,
    PE0_APWM3_A      = 7U,
    PE0_PWM0         = 8U,
    PE1_DVP_D1       = 3U,
    PE1_I2C0_SDA     = 4U,
    PE1_GMAC0_RXD0   = 6U,
    PE1_APWM3_B      = 7U,
    PE1_PWM1         = 8U,
    PE2_DVP_D2       = 3U,
    PE2_CAN0_TX      = 4U,
    PE2_UART4_TX     = 5U,
    PE2_GMAC0_RXCTL  = 6U,
    PE2_APWM4_A      = 7U,
    PE2_PWM2         = 8U,
    PE3_DVP_D3       = 3U,
    PE3_CAN0_RX      = 4U,
    PE3_UART4_RX     = 5U,
    PE3_GMAC0_CLKIN  = 6U,
    PE3_APWM4_B      = 7U,
    PE3_PWM3         = 8U,
    PE4_DVP_D4       = 3U,
    PE4_CAN1_TX      = 4U,
    PE4_UART5_TX     = 5U,
    PE4_GMAC0_TXD1   = 6U,
    PE4_APWM5_A      = 7U,
    PE4_PWM4         = 8U,
    PE5_DVP_D5       = 3U,
    PE5_CAN1_RX      = 4U,
    PE5_UART5_RX     = 5U,
    PE5_GMAC0_TXD0   = 6U,
    PE5_APWM5_B      = 7U,
    PE5_PWM5         = 8U,
    PE6_SPK0         = 2U,
    PE6_DVP_D6       = 3U,
    PE6_UART5_RTS    = 4U,
    PE6_UART6_TX     = 5U,
    PE6_GMAC0_TXCK   = 6U,
    PE6_QEP1_H0      = 7U,
    PE6_CAP0         = 8U,
    PE7_SPK1         = 2U,
    PE7_DVP_D7       = 3U,
    PE7_UART7_RTS    = 4U,
    PE7_UART6_RX     = 5U,
    PE7_GMAC0_TXCTL  = 6U,
    PE7_QEP1_H1      = 7U,
    PE7_CAP1         = 8U,
    PE8_I2S0_MCLK    = 2U,
    PE8_DVP_CK       = 3U,
    PE8_UART6_RTS    = 4U,
    PE8_UART7_TX     = 5U,
    PE8_GMAC0_MDC    = 6U,
    PE8_QEP1_H2      = 7U,
    PE8_CAP2         = 8U,
    PE9_I2S0_BCLK    = 2U,
    PE9_DVP_HS       = 3U,
    PE9_UART6_CTS    = 4U,
    PE9_UART7_RX     = 5U,
    PE9_GMAC0_MDIO   = 6U,
    PE9_QEP1_A       = 7U,
    PE10_I2S0_LRCK   = 2U,
    PE10_DVP_VS      = 3U,
    PE10_SPK0        = 4U,
    PE10_CLK_OUT2    = 6U,
    PE10_QEP1_B      = 7U,
    PE10_EPHY_LED0   = 8U,
    PE11_I2S0_DOUT   = 2U,
    PE11_I2S0_DIN    = 3U,
    PE11_SPK1        = 4U,
    PE11_CLK_OUT1    = 5U,
    PE11_GMAC0_RXD3  = 6U,
    PE11_QEP1_I      = 7U,
    PE11_EPHY_LED1   = 8U,
    PE12_I2S0_DIN    = 2U,
    PE12_SPI3_CLK    = 3U,
    PE12_DMIC_CLK    = 4U,
    PE12_I2C2_SCK    = 5U,
    PE12_GMAC0_RXD2  = 6U,
    PE12_QEP1_S      = 7U,
    PE13_SPI3_CS     = 3U,
    PE13_DMIC_D0     = 4U,
    PE13_I2C2_SDA    = 5U,
    PE13_GMAC0_RXCK  = 6U,
    PE13_CAP0        = 7U,
    PE14_SPI3_MOSI   = 3U,
    PE14_UART3_TX    = 5U,
    PE14_GMAC0_TXD3  = 6U,
    PE14_CAP1        = 7U,
    PE15_SPI3_MISO   = 3U,
    PE15_UART3_RX    = 5U,
    PE15_GMAC0_TXD2  = 6U,
    PE15_CAP2        = 7U,
    PE16_SPI0_CLK    = 3U,
    PE16_CAN0_TX     = 4U,
    PE16_I2C3_SCK    = 5U,
    PE16_GMAC0_TRIG  = 6U,
    PE17_SPI0_CS     = 3U,
    PE17_CAN0_RX     = 4U,
    PE17_I2C3_SDA    = 5U,
    PE17_GMAC0_PPSO  = 6U,
    PE18_SPI0_MOSI   = 3U,
    PE18_CAN1_TX     = 4U,
    PE18_PWM6        = 5U,
    PE18_GMAC1_TRIG  = 6U,
    PE19_SPI0_MISO   = 3U,
    PE19_CAN1_RX     = 4U,
    PE19_PWM7        = 5U,
    PE19_GMAC1_PPSO  = 6U,
    PF0_SDC2_D1      = 2U,
    PF0_SPI2_CLK     = 3U,
    PF0_UART5_TX     = 5U,
    PF0_GMAC1_RXD1   = 6U,
    PF0_PBUS_AD0     = 7U,
    PF0_GMAC1_RXD0   = 8U,
    PF1_SDC2_D0      = 2U,
    PF1_SPI2_CS      = 3U,
    PF1_UART5_RX     = 5U,
    PF1_GMAC1_RXD0   = 6U,
    PF1_PBUS_AD1     = 7U,
    PF2_SDC2_CLK     = 2U,
    PF2_SPI2_MOSI    = 3U,
    PF2_UART5_RTS    = 5U,
    PF2_GMAC1_RXCTL  = 6U,
    PF2_PBUS_AD2     = 7U,
    PF2_GMAC1_RXD1   = 8U,
    PF3_SDC2_CMD     = 2U,
    PF3_SPI2_MISO    = 3U,
    PF3_UART5_CTS    = 5U,
    PF3_GMAC1_CLKIN  = 6U,
    PF3_PBUS_AD3     = 7U,
    PF4_SDC2_D3      = 2U,
    PF4_UART6_TX     = 5U,
    PF4_GMAC1_TXD1   = 6U,
    PF4_PBUS_AD4     = 7U,
    PF4_DBG_IO26     = 8U,
    PF5_SDC2_D2      = 2U,
    PF5_UART6_RX     = 5U,
    PF5_GMAC1_TXD0   = 6U,
    PF5_PBUS_AD5     = 7U,
    PF5_DBG_IO27     = 8U,
    PF6_UART7_TX     = 5U,
    PF6_GMAC1_TXCK   = 6U,
    PF6_PBUS_AD6     = 7U,
    PF6_DBG_IO28     = 8U,
    PF7_UART7_RX     = 5U,
    PF7_GMAC1_TXCTL  = 6U,
    PF7_PBUS_AD7     = 7U,
    PF7_DBG_IO29     = 8U,
    PF8_UART7_RTS    = 5U,
    PF8_GMAC1_MDC    = 6U,
    PF8_PBUS_AD8     = 7U,
    PF8_DBG_IO30     = 8U,
    PF9_UART7_CTS    = 5U,
    PF9_GMAC1_MDIO   = 6U,
    PF9_PBUS_AD9     = 7U,
    PF9_DBG_IO31     = 8U,
    PF10_I2S1_MCLK   = 2U,
    PF10_I2S1_DIN    = 3U,
    PF10_UART3_CTS   = 5U,
    PF10_CLK_OUT3    = 6U,
    PF10_PBUS_AD10   = 7U,
    PF10_GMAC1_CLKIN = 8U,
    PF11_I2S1_BCLK   = 2U,
    PF11_PBUS_AD11   = 4U,
    PF11_UART3_TX    = 5U,
    PF11_GMAC1_RXD3  = 6U,
    PF11_PBUS_CLK    = 7U,
    PF12_I2S1_LRCK   = 2U,
    PF12_UART4_RTS   = 4U,
    PF12_UART3_RX    = 5U,
    PF12_GMAC1_RXD2  = 6U,
    PF12_PBUS_NCS    = 7U,
    PF12_GMAC1_TXD0  = 8U,
    PF13_I2S1_DOUT   = 2U,
    PF13_I2S1_DIN    = 3U,
    PF13_UART4_CTS   = 4U,
    PF13_UART3_RTS   = 5U,
    PF13_GMAC1_RXCK  = 6U,
    PF13_PBUS_NADV   = 7U,
    PF13_GMAC1_RXCTL = 8U,
    PF14_I2S1_DIN    = 2U,
    PF14_SPK0        = 3U,
    PF14_DMIC_D0     = 4U,
    PF14_UART4_TX    = 5U,
    PF14_GMAC1_TXD3  = 6U,
    PF14_PBUS_NWE    = 7U,
    PF15_DE_TE       = 2U,
    PF15_SPK1        = 3U,
    PF15_DMIC_CLK    = 4U,
    PF15_UART4_RX    = 5U,
    PF15_GMAC1_TXD2  = 6U,
    PF15_PBUS_NOE    = 7U,
    PG0_USB0_DM      = 2U,
    PG0_UART0_RX     = 4U,
    PG0_UART1_RX     = 5U,
    PG1_USB0_DP      = 2U,
    PG1_UART0_TX     = 4U,
    PG1_UART1_TX     = 5U,
    PG2_USB1_DM      = 2U,
    PG2_UART0_RX     = 4U,
    PG2_UART2_RX     = 5U,
    PG3_USB1_DP      = 2U,
    PG3_UART0_TX     = 4U,
    PG3_UART2_TX     = 5U,

    PIN_FUNC_DISABLED = 0U,
    PIN_FUNC_GPIO     = 1U,
} pin_func_t;

#ifdef __cplusplus
}
#endif

#endif /* __AIC_GPIO_ID_H__ */
