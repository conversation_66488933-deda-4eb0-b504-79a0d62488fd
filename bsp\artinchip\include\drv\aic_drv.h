/*
 * Copyright (c) 2022, Artinchip Technology Co., Ltd
 *
 * SPDX-License-Identifier: Apache-2.0
 */

#ifndef _ARTINCHIP_AIC_DRV_H_
#define _ARTINCHIP_AIC_DRV_H_

#ifdef __cplusplus
extern "C" {
#endif

#include <aic_hal.h>
#include "aic_drv_irq.h"
#include "aic_drv_gpio.h"
#include "aic_drv_uart.h"
#include "aic_drv_ge.h"
#include "drv_qspi.h"
#include "drv_efuse.h"
#include "drv_dma.h"

#ifdef __cplusplus
}
#endif

#endif /* _ARTINCHIP_AIC_DRV_H_ */
