Import('AIC_ROOT')
Import('PRJ_KERNEL')
from building import *

cwd = GetCurrentDir()
CPPPATH = []
src = []
if GetDepend('AIC_TSEN_TEST') and GetDepend('RT_USING_FINSH'):
    src = Glob('*test_tsen.c')

if GetDepend('AIC_TSEN_HIGH_TEMP_ALARM_TEST') and GetDepend('RT_USING_FINSH'):
    src = Glob('*test_tsen_htp.c')

group = DefineGroup('test-tsen', src, depend = [''], CPPPATH = CPPPATH)

Return('group')
