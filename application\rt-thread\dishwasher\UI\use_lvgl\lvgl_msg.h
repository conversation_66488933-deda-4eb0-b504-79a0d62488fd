#ifndef _LVGL_MSG_H_
#define _LVGL_MSG_H_

#include <rtthread.h>
#include "user_api.h"

#define LVGL_MQ_SIZE 8

typedef enum {
    LVGL_MSG_SET_ACTIVE_SCREEN,
    LVGL_MSG_GET_ACTIVE_SCREEN,
    LVGL_MSG_DEBUG_SET_TEMPERATURE,
    LVGL_MSG_DEBUG_SET_24V_OUTPUT,
    LVGL_MSG_DEBUG_SET_SIGNAL,
    LVGL_MSG_DEBUG_CLEAR_SIGNAL,
    LVGL_MSG_DEBUG_SET_COIL,
    LVGL_MSG_DEBUG_CLEAR_COIL,
    LVGL_MSG_DEBUG_CHOOSE_FOCUS,
    LVGL_MSG_DEBUG_GET_FOCUS,
    LVGL_MSG_DEVELOPER_CHANGE_LABEL,
    LVGL_MSG_PARAMETER_CHOOSE_VALUE,
    LVGL_MSG_PARAMETER_CHANGE_PARAM,
    LVGL_MSG_PARAMETER_CHANGE_VALUE,
    LVGL_MSG_RUN_CHOOSE_WORKMODE,
    LVGL_MSG_RUN_CHOOSE_FOCUS,
    LVGL_MSG_RUN_ADD_FOCUS,
    LVGL_MSG_RUN_CLEAR_FOCUS,
    LVGL_MSG_RUN_GET_FOCUSBIT,
    LVGL_MSG_RUN_SET_WASH_WATER_LEVEL,
    LVGL_MSG_RUN_SET_RINSE_WATER_LEVEL,
    LVGL_MSG_RUN_SET_WASH_PRESET_TEMP,
    LVGL_MSG_RUN_SET_WASH_ACTUAL_TEMP,
    LVGL_MSG_RUN_SET_RINSE_PRESET_TEMP,
    LVGL_MSG_RUN_SET_RINSE_ACTUAL_TEMP,
    LVGL_MSG_RUN_SET_PROGRESS,
    LVGL_MSG_RUN_SHOW_WARNING,
    LVGL_MSG_RUN_HIDE_WARNING,
    LVGL_MSG_RUN_SET_DOOR_STATE,
    LVGL_MSG_SETTING_CHOOSE_PARAM,
    LVGL_MSG_SETTING_CHANGE_VALUE,
    LVGL_MSG_SETTING_BIND,
    LVGL_MSG_SETTING_CHANGE_PARAM_STRING,
    LVGL_MSG_STANDBY_SET_ROLLER,
    LVGL_MSG_STANDBY_GET_ROLLER,
} lvgl_msg_type_t;

typedef struct {
    lvgl_msg_type_t type;
    union {
        struct {
            uint8_t page;
        } set_screen;
        struct {
            uint8_t index;
            int value;
        } debug_temp;
        struct {
            uint8_t index;
            int value;
        } debug_24v;
        struct {
            uint32_t maskbit;
        } debug_signal;
        struct {
            uint32_t maskbit;
        } debug_coil;
        struct {
            uint8_t index;
        } debug_focus;
        struct {
            uint8_t index;
            char string[64];
        } developer_label;
        struct {
            int8_t index;
        } param_choose;
        struct {
            uint8_t index;
            char string[64];
        } param_change;
        struct {
            uint8_t workmode;
        } run_workmode;
        struct {
            uint8_t index;
        } run_focus;
        struct {
            uint32_t maskbit;
        } run_focus_bit;
        struct {
            uint8_t level;
        } run_water_level;
        struct {
            int16_t temperature;
        } run_temperature;
        struct {
            uint8_t progress;
        } run_progress;
        struct {
            char string[64];
        } run_warning;
        struct {
            uint8_t state;
        } run_door;
        struct {
            uint8_t index;
        } setting_param;
        struct {
            char string[64];
        } setting_value;
        struct {
            uint8_t index;
            char string[64];
        } setting_param_string;
        struct {
            uint8_t index;
        } standby_roller;
    } data;
    rt_sem_t response_sem;  // 用于同步返回值
    union {
        uint8_t uint8_ret;
        int int_ret;
        uint16_t uint16_ret;
    } result;
} lvgl_msg_t;

rt_err_t lvgl_msg_init(void);
rt_err_t lvgl_msg_send(lvgl_msg_t *msg);
rt_err_t lvgl_msg_send_sync(lvgl_msg_t *msg);
void lvgl_msg_process_task(void);

#endif