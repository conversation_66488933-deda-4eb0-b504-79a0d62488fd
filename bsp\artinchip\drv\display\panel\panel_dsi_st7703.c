/*
 * Copyright (c) 2023-2024, ArtInChip Technology Co., Ltd
 *
 * SPDX-License-Identifier: Apache-2.0
 */

#include "panel_com.h"
#include "panel_dsi.h"
#include <aic_hal.h>

#define ST7703_ENABLE_PIN   "PE.1"
#define ST7703_RESET_PIN    "PE.2"

static struct gpio_desc reset_gpio;
static struct gpio_desc sleep_gpio;

static void panel_gpio_init(struct aic_panel *panel)
{
    panel_get_gpio(&reset_gpio, ST7703_RESET_PIN);
    panel_get_gpio(&sleep_gpio, ST7703_ENABLE_PIN);

    panel_gpio_set_value(&sleep_gpio, 1);
    aic_delay_ms(1);

    panel_gpio_set_value(&reset_gpio, 1);
    aic_delay_ms(100);
    panel_gpio_set_value(&reset_gpio, 0);
    aic_delay_ms(20);
    panel_gpio_set_value(&reset_gpio, 1);
    aic_delay_ms(10);
}

static int panel_enable(struct aic_panel *panel)
{
    int ret;

    panel_gpio_init(panel);

    panel_di_enable(panel, 0);
    panel_dsi_send_perpare(panel);

    panel_dsi_generic_send_seq(panel, 0xB9, 0xF1, 0x12, 0x83);

    panel_dsi_generic_send_seq(panel, 0xBA, 0x33, 0x81, 0x05, 0xF9, 0x0E, 0x0E,
            0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x44, 0x25, 0x00,
            0x91, 0x0A, 0x00, 0x00, 0x02, 0x4F, 0xD1, 0x00, 0x00, 0x37);

    panel_dsi_generic_send_seq(panel, 0xB8, 0x24, 0x22, 0x20, 0x03);

    panel_dsi_generic_send_seq(panel, 0xBF, 0x02, 0x11, 0x00);

    panel_dsi_generic_send_seq(panel, 0xB3, 0x10, 0x10, 0x0A, 0x50, 0x03, 0xFF,
            0x00, 0x00, 0x00, 0x00);

    panel_dsi_generic_send_seq(panel, 0xC0, 0x73, 0x73, 0x50, 0x50, 0x00, 0x00,
            0x08, 0x70, 0x00);

    panel_dsi_generic_send_seq(panel, 0xBC, 0x46);

    panel_dsi_generic_send_seq(panel, 0xCC, 0x0B);

    panel_dsi_generic_send_seq(panel, 0xB4, 0x80);

    panel_dsi_generic_send_seq(panel, 0xB2, 0x3C, 0x12, 0x30);

    panel_dsi_generic_send_seq(panel, 0xE3, 0x07, 0x07, 0x0B, 0x0B, 0x03, 0x0B,
            0x00, 0x00, 0x00, 0x00, 0xFF, 0x00, 0x40, 0x10);

    panel_dsi_generic_send_seq(panel, 0xC1, 0x65, 0x00, 0x32, 0x32, 0x77, 0xF1,
            0xCC, 0xCC, 0x77, 0x77, 0x33, 0x33);

    panel_dsi_generic_send_seq(panel, 0xB5, 0x0A, 0x0A);

    panel_dsi_generic_send_seq(panel, 0xB6, 0xB7, 0xB7);

    panel_dsi_generic_send_seq(panel, 0xE9, 0x88, 0x10, 0x0A, 0x10, 0x0F, 0xA1,
            0x80, 0x12, 0x31, 0x23, 0x47, 0x86, 0xA1, 0x80, 0x47, 0x08, 0x04,
            0x44, 0x00, 0x00, 0x00, 0x00, 0x04, 0x44, 0x00, 0x00, 0x00, 0x00,
            0x75, 0x31, 0x88, 0x88, 0x88, 0x1F, 0x88, 0x38, 0xFF, 0x58, 0x88,
            0x64, 0x20, 0x88, 0x88, 0x88, 0x0F, 0x88, 0x28, 0xFF, 0x48, 0x88,
            0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
            0x00, 0x00);

    panel_dsi_generic_send_seq(panel, 0xEA, 0x00, 0x1A, 0x00, 0x00, 0x00, 0x00,
            0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x46, 0x88, 0x88, 0x88,
            0x28, 0x8F, 0x08, 0xFF, 0x48, 0x88, 0x13, 0x57, 0x88, 0x88, 0x88,
            0x38, 0x8F, 0x18, 0xFF, 0x58, 0x88, 0x23, 0x10, 0x00, 0x01, 0x02,
            0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
            0x00, 0x00, 0x00, 0x00, 0x40, 0xA1, 0x80, 0x00, 0x00, 0x00, 0x00);

    panel_dsi_generic_send_seq(panel, 0xE0, 0x00, 0x03, 0x06, 0x2D, 0x3E, 0x3F,
            0x34, 0x32, 0x08, 0x0C, 0x0D, 0x10, 0x12, 0x11, 0x12, 0x10, 0x15,
            0x00, 0x03, 0x06, 0x2D, 0x3E, 0x3F, 0x34, 0x32, 0x08, 0x0C, 0x0D,
            0x10, 0x12, 0x11, 0x12, 0x10, 0x15);

    ret = panel_dsi_dcs_exit_sleep_mode(panel);
    if (ret < 0) {
        pr_err("Failed to exit sleep mode: %d\n", ret);
        return ret;
    }
    aic_delay_ms(120);

    ret = panel_dsi_dcs_set_display_on(panel);
    if (ret < 0) {
        pr_err("Failed to set display on: %d\n", ret);
        return ret;
    }
    aic_delay_ms(5);

    panel_dsi_setup_realmode(panel);

    panel_de_timing_enable(panel, 0);
    panel_backlight_enable(panel, 0);
    return 0;
}

static struct aic_panel_funcs panel_funcs = {
    .disable = panel_default_disable,
    .unprepare = panel_default_unprepare,
    .prepare = panel_default_prepare,
    .enable = panel_enable,
    .register_callback = panel_register_callback,
};

static struct display_timing st7703_timing = {
    .pixelclock = 60000000,
    .hactive = 720,
    .hfront_porch = 180,
    .hback_porch = 180,
    .hsync_len = 100,
    .vactive = 720,
    .vfront_porch = 40,
    .vback_porch = 22,
    .vsync_len = 18,
};

struct panel_dsi dsi = {
    .mode = DSI_MOD_VID_BURST,
    .format = DSI_FMT_RGB888,
    .lane_num = 4,
};

struct aic_panel dsi_st7703 = {
    .name = "panel-st7703",
    .timings = &st7703_timing,
    .funcs = &panel_funcs,
    .dsi = &dsi,
    .connector_type = AIC_MIPI_COM,
};

