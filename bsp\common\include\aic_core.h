/*
 * Copyright (c) 2022, Artinchip Technology Co., Ltd
 *
 * SPDX-License-Identifier: Apache-2.0
 */

#ifndef __AIC_CORE_H__
#define __AIC_CORE_H__

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>
#include <sys_freq.h>
#include <rtconfig.h>
#include <aic_soc.h>
#include <aic_arch.h>
#include <aic_common.h>
#include <aic_reboot_reason.h>
#include <aic_io.h>
#include <aic_errno.h>
#include <aic_list.h>
#include <aic_log.h>
#include <aic_osal.h>

#ifdef __cplusplus
}
#endif

#endif /* __AIC_CORE_H__ */
