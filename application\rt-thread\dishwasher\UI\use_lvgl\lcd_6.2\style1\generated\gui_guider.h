/*
* Copyright 2025 NXP
* NXP Proprietary. This software is owned or controlled by NXP and may only be used strictly in
* accordance with the applicable license terms. By expressly accepting such terms or by downloading, installing,
* activating and/or otherwise using the software, you are agreeing that you have read, and that you agree to
* comply with and are bound by, such license terms.  If you do not agree to be bound by the applicable license
* terms, then you may not retain, install, activate or otherwise use the software.
*/

#ifndef GUI_GUIDER_H
#define GUI_GUIDER_H
#ifdef __cplusplus
extern "C" {
#endif

#include "lvgl.h"

typedef struct
{
  
	lv_obj_t *lcd_start;
	bool lcd_start_del;
	lv_obj_t *lcd_start_spinner_1;
	lv_obj_t *lcd_start_label_1;
	lv_obj_t *lcd_standby;
	bool lcd_standby_del;
	lv_obj_t *lcd_standby_animimg_1;
	lv_obj_t *lcd_standby_roller_1;
	lv_obj_t *lcd_standby_qrcode_1;
	lv_obj_t *lcd_run;
	bool lcd_run_del;
	lv_obj_t *lcd_run_run_animimg;
	lv_obj_t *lcd_run_rinsetemp_cont;
	lv_obj_t *lcd_run_rinsetemp_img_2;
	lv_obj_t *lcd_run_rinsetemp_label_5;
	lv_obj_t *lcd_run_rinsetemp_img_1;
	lv_obj_t *lcd_run_rinsetemp_label_4;
	lv_obj_t *lcd_run_rinsetemp_labe_3;
	lv_obj_t *lcd_run_rinsetemp_label_2;
	lv_obj_t *lcd_run_rinsetemp_line;
	lv_obj_t *lcd_run_rinsetemp_label_1;
	lv_obj_t *lcd_run_washtemp_cont;
	lv_obj_t *lcd_run_washtemp_img_2;
	lv_obj_t *lcd_run_washtemp_label_5;
	lv_obj_t *lcd_run_washtemp_label_4;
	lv_obj_t *lcd_run_washtemp_label_3;
	lv_obj_t *lcd_run_washtemp_label_2;
	lv_obj_t *lcd_run_washtemp_line;
	lv_obj_t *lcd_run_washtemp_label_1;
	lv_obj_t *lcd_run_washtemp_img_1;
	lv_obj_t *lcd_run_workmode_cont;
	lv_obj_t *lcd_run_workmode_label_1;
	lv_obj_t *lcd_run_workmode_label_2;
	lv_obj_t *lcd_run_workmode_label_3;
	lv_obj_t *lcd_run_wash_cont;
	lv_obj_t *lcd_run_wash_img;
	lv_obj_t *lcd_run_wash_line;
	lv_obj_t *lcd_run_wash_label3;
	lv_obj_t *lcd_run_wash_label2;
	lv_obj_t *lcd_run_wash_label;
	lv_obj_t *lcd_run_rinse_cont;
	lv_obj_t *lcd_run_rinse_label;
	lv_obj_t *lcd_run_rinse_label3;
	lv_obj_t *lcd_run_rinse_line;
	lv_obj_t *lcd_run_rinse_label2;
	lv_obj_t *lcd_run_rinse_img;
	lv_obj_t *lcd_run_label_3;
	lv_obj_t *lcd_run_label_4;
	lv_obj_t *lcd_run_door_cont;
	lv_obj_t *lcd_run_door_img;
	lv_obj_t *lcd_run_door_label;
	lv_obj_t *lcd_run_door_line_1;
	lv_obj_t *lcd_run_door_line_2;
	lv_obj_t *lcd_run_label_2;
	lv_obj_t *lcd_run_wait_cont;
	lv_obj_t *lcd_run_wait_canvas;
	lv_obj_t *lcd_run_wait_label;
	lv_obj_t *lcd_run_warning_cont;
	lv_obj_t *lcd_run_warning_img;
	lv_obj_t *lcd_run_warning_label;
	lv_obj_t *lcd_run_label_1;
	lv_obj_t *lcd_parameter;
	bool lcd_parameter_del;
	lv_obj_t *lcd_parameter_cont_1;
	lv_obj_t *lcd_parameter_line_1;
	lv_obj_t *lcd_parameter_cont_2;
	lv_obj_t *lcd_parameter_line_2;
	lv_obj_t *lcd_parameter_cont_3;
	lv_obj_t *lcd_parameter_line_3;
	lv_obj_t *lcd_parameter_cont_4;
	lv_obj_t *lcd_parameter_line_4;
	lv_obj_t *lcd_parameter_line_5;
	lv_obj_t *lcd_parameter_line_6;
	lv_obj_t *lcd_parameter_line_7;
	lv_obj_t *lcd_parameter_list_1;
	lv_obj_t *lcd_parameter_label_1;
	lv_obj_t *lcd_developer;
	bool lcd_developer_del;
	lv_obj_t *lcd_developer_cont_1;
	lv_obj_t *lcd_setting;
	bool lcd_setting_del;
	lv_obj_t *lcd_setting_cont_1;
	lv_obj_t *lcd_setting_line_1;
	lv_obj_t *lcd_setting_line_2;
	lv_obj_t *lcd_setting_list_1;
	lv_obj_t *lcd_setting_list_1_item0;
	lv_obj_t *lcd_setting_list_1_item1;
	lv_obj_t *lcd_setting_list_1_item2;
	lv_obj_t *lcd_setting_list_1_item3;
	lv_obj_t *lcd_debug;
	bool lcd_debug_del;
	lv_obj_t *lcd_debug_img_1;
	lv_obj_t *lcd_debug_label_1;
	lv_obj_t *lcd_debug_label_2;
	lv_obj_t *lcd_debug_label_3;
	lv_obj_t *lcd_debug_label_4;
	lv_obj_t *lcd_debug_label_5;
	lv_obj_t *lcd_debug_label_6;
	lv_obj_t *lcd_debug_label_7;
	lv_obj_t *lcd_debug_label_8;
	lv_obj_t *lcd_debug_label_9;
	lv_obj_t *lcd_debug_label_10;
	lv_obj_t *lcd_debug_label_11;
	lv_obj_t *lcd_debug_label_12;
	lv_obj_t *lcd_debug_label_13;
	lv_obj_t *lcd_debug_label_14;
	lv_obj_t *lcd_debug_label_15;
	lv_obj_t *lcd_debug_label_16;
	lv_obj_t *lcd_debug_label_17;
	lv_obj_t *lcd_debug_label_18;
	lv_obj_t *lcd_debug_label_19;
	lv_obj_t *lcd_debug_label_20;
	lv_obj_t *lcd_debug_label_21;
	lv_obj_t *lcd_debug_label_22;
	lv_obj_t *lcd_debug_label_23;
	lv_obj_t *lcd_debug_sw_1;
	lv_obj_t *lcd_debug_sw_2;
	lv_obj_t *lcd_debug_sw_3;
	lv_obj_t *lcd_debug_sw_4;
	lv_obj_t *lcd_debug_sw_5;
	lv_obj_t *lcd_debug_sw_6;
	lv_obj_t *lcd_debug_label_24;
	lv_obj_t *lcd_debug_label_25;
	lv_obj_t *lcd_debug_label_26;
	lv_obj_t *lcd_debug_label_27;
	lv_obj_t *lcd_debug_label_28;
	lv_obj_t *lcd_debug_cont_1;
	lv_obj_t *lcd_debug_label_29;
	lv_obj_t *lcd_debug_cont_2;
	lv_obj_t *lcd_debug_label_30;
	lv_obj_t *lcd_debug_label_31;
	lv_obj_t *lcd_debug_label_32;
	lv_obj_t *lcd_debug_label_33;
	lv_obj_t *lcd_debug_label_34;
	lv_obj_t *lcd_debug_label_35;
	lv_obj_t *lcd_debug_label_36;
	lv_obj_t *lcd_debug_label_37;
	lv_obj_t *lcd_debug_label_38;
	lv_obj_t *lcd_debug_label_39;
	lv_obj_t *lcd_debug_label_40;
	lv_obj_t *lcd_debug_label_41;
	lv_obj_t *lcd_debug_label_42;
	lv_obj_t *lcd_debug_label_43;
	lv_obj_t *lcd_debug_label_44;
	lv_obj_t *lcd_debug_label_45;
	lv_obj_t *lcd_debug_label_46;
	lv_obj_t *lcd_debug_label_47;
	lv_obj_t *lcd_debug_label_48;
	lv_obj_t *lcd_debug_label_49;
	lv_obj_t *lcd_debug_cont_3;
	lv_obj_t *lcd_debug_sw_7;
	lv_obj_t *lcd_debug_cont_4;
	lv_obj_t *lcd_debug_sw_8;
	lv_obj_t *lcd_debug_cont_5;
	lv_obj_t *lcd_debug_sw_9;
	lv_obj_t *lcd_debug_cont_6;
	lv_obj_t *lcd_debug_sw_10;
	lv_obj_t *lcd_debug_cont_7;
	lv_obj_t *lcd_debug_sw_11;
	lv_obj_t *lcd_debug_cont_8;
	lv_obj_t *lcd_debug_sw_12;
	lv_obj_t *lcd_debug_cont_9;
	lv_obj_t *lcd_debug_sw_13;
	lv_obj_t *lcd_debug_cont_10;
	lv_obj_t *lcd_debug_sw_14;
	lv_obj_t *lcd_debug_cont_11;
	lv_obj_t *lcd_debug_sw_15;
	lv_obj_t *lcd_debug_line_1;
	lv_obj_t *lcd_debug_line_2;
	lv_obj_t *lcd_debug_line_3;
	lv_obj_t *lcd_debug_label_50;
}lv_ui;

typedef void (*ui_setup_scr_t)(lv_ui * ui);

void ui_init_style(lv_style_t * style);

void ui_load_scr_animation(lv_ui *ui, lv_obj_t ** new_scr, bool new_scr_del, bool * old_scr_del, ui_setup_scr_t setup_scr,
                           lv_scr_load_anim_t anim_type, uint32_t time, uint32_t delay, bool is_clean, bool auto_del);

void ui_animation(void * var, int32_t duration, int32_t delay, int32_t start_value, int32_t end_value, lv_anim_path_cb_t path_cb,
                       uint16_t repeat_cnt, uint32_t repeat_delay, uint32_t playback_time, uint32_t playback_delay,
                       lv_anim_exec_xcb_t exec_cb, lv_anim_start_cb_t start_cb, lv_anim_ready_cb_t ready_cb, lv_anim_deleted_cb_t deleted_cb);


void init_scr_del_flag(lv_ui *ui);

void setup_ui(lv_ui *ui);


extern lv_ui guider_ui;


void setup_scr_lcd_start(lv_ui *ui);
void setup_scr_lcd_standby(lv_ui *ui);
void setup_scr_lcd_run(lv_ui *ui);
void setup_scr_lcd_parameter(lv_ui *ui);
void setup_scr_lcd_developer(lv_ui *ui);
void setup_scr_lcd_setting(lv_ui *ui);
void setup_scr_lcd_debug(lv_ui *ui);
#include "extra/widgets/animimg/lv_animimg.h"

#ifdef __cplusplus
}
#endif
#endif
