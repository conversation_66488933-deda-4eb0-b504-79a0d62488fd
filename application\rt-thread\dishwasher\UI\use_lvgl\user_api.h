#ifndef _USER_API_H_
#define _USER_API_H_
#include "gui_guider.h"
#include "guider_customer_fonts.h"
#include "aic_ui.h"
#define COUNT_OF(array) (sizeof(array) / sizeof((array)[0]))

#define USER_ASSERT_NULL LV_ASSERT_NULL
#define USE_MALLOC 0

#if USE_MALLOC
#define USER_MALLOC malloc
#define USER_FREE free
#endif
#define UI_STING_LENGTH 64

extern lv_ui guider_ui;
extern lv_ft_info_t lv_ft_info;
enum UI_RETURN_TYPE
{
    UI_NO_ERR,
    UI_OVERFLOW,
    UI_NO_SPACE,
    UI_MATCH_ERR
};

#define LCD_PAGE_LIST              \
    LCD_PAGE_X(LCD_START),         \
        LCD_PAGE_X(LCD_STANDBY),   \
        LCD_PAGE_X(LCD_RUN),       \
        LCD_PAGE_X(LCD_SETTING),   \
        LCD_PAGE_X(LCD_DEBUG),     \
        LCD_PAGE_X(LCD_PARAMETER), \
        LCD_PAGE_X(LCD_DEVELOPER)

enum DISP_PAGE
{
#define LCD_PAGE_X(PAGE) PAGE
    LCD_PAGE_LIST,
#undef LCD_PAGE_X
    PAGE_NUM
};
uint8_t get_active_screen_id(void);
void set_active_screen(enum DISP_PAGE _page);

uint8_t get_active_screen_id_os(void);
void set_active_screen_os(enum DISP_PAGE _page); // 切换界面

/*LCD DEBUG*/

#define LCD_DEBUG_ITEM_LIST                  \
    LCD_DEBUG_X(LCD_DEBUG_24V_OUTPUT1),      \
        LCD_DEBUG_X(LCD_DEBUG_24V_OUTPUT2),  \
        LCD_DEBUG_X(LCD_DEBUG_SIGNAL1),      \
        LCD_DEBUG_X(LCD_DEBUG_SIGNAL2),      \
        LCD_DEBUG_X(LCD_DEBUG_SIGNAL3),      \
        LCD_DEBUG_X(LCD_DEBUG_SIGNAL4),      \
        LCD_DEBUG_X(LCD_DEBUG_SIGNAL5),      \
        LCD_DEBUG_X(LCD_DEBUG_SIGNAL6),      \
        LCD_DEBUG_X(LCD_DEBUG_TEMPERATURE1), \
        LCD_DEBUG_X(LCD_DEBUG_TEMPERATURE2), \
        LCD_DEBUG_X(LCD_DEBUG_TEMPERATURE3), \
        LCD_DEBUG_X(LCD_DEBUG_COIL1),        \
        LCD_DEBUG_X(LCD_DEBUG_COIL2),        \
        LCD_DEBUG_X(LCD_DEBUG_COIL3),        \
        LCD_DEBUG_X(LCD_DEBUG_COIL4),        \
        LCD_DEBUG_X(LCD_DEBUG_COIL5),        \
        LCD_DEBUG_X(LCD_DEBUG_COIL6),        \
        LCD_DEBUG_X(LCD_DEBUG_COIL7),        \
        LCD_DEBUG_X(LCD_DEBUG_COIL8),        \
        LCD_DEBUG_X(LCD_DEBUG_COIL9)

enum LCD_DEBUG_ITEM_INDEX
{
#define LCD_DEBUG_X(name) name##_INDEX
    LCD_DEBUG_ITEM_LIST,
#undef LCD_DEBUG_X
    LCD_DEBUG_ITEM_NUM
};

enum LCD_DEBUG_ITEM_BITMASK
{
#define LCD_DEBUG_X(name) name##_BITMASK = 1 << name##_INDEX
    LCD_DEBUG_ITEM_LIST,
#undef LCD_DEBUG_X
    LCD_DEBUG_ALL_BITMASK = (1 << LCD_DEBUG_ITEM_NUM) - 1
};

void lcd_debug_setup_init(void);
void lcd_debug_touch_cb(lv_event_t *e);

int lcd_debug_get_current_focus_index(void);
void lcd_debug_choose_focus_obj(enum LCD_DEBUG_ITEM_INDEX _index);
void lcd_debug_set_temperature(enum LCD_DEBUG_ITEM_INDEX _index, int _num);
void lcd_debug_set_24v_output(enum LCD_DEBUG_ITEM_INDEX _index, int _num);
void lcd_debug_set_signal(uint32_t _maskbit);
void lcd_debug_clear_signal(uint32_t _maskbit);
void lcd_debug_set_coil(uint32_t _maskbit);
void lcd_debug_clear_coil(uint32_t _maskbit);

int lcd_debug_get_current_focus_index_os(void);                                // 获取光标
void lcd_debug_choose_focus_obj_os(enum LCD_DEBUG_ITEM_INDEX _index);          // 设置光标
void lcd_debug_set_temperature_os(enum LCD_DEBUG_ITEM_INDEX _index, int _num); // 改变温度
void lcd_debug_set_24v_output_os(enum LCD_DEBUG_ITEM_INDEX _index, int _num);  // 改变24V输出
void lcd_debug_set_signal_os(uint32_t _maskbit);                               // 设置信号
void lcd_debug_clear_signal_os(uint32_t _maskbit);                             // 清除信号
void lcd_debug_set_coil_os(uint32_t _maskbit);                                 // 设置继电器
void lcd_debug_clear_coil_os(uint32_t _maskbit);                               // 清除继电器

/*LCD DEVELOPER*/

#define LCD_DEVELOPER_ITEM_LIST                         \
    LCD_DEVELOPER_X(CUSTOMER_COMPANY_NAME, "公司名称"), \
        LCD_DEVELOPER_X(SOFTWARE_VERSION, "V0.0.1"),    \
        LCD_DEVELOPER_X(RELEASE_DATE, "2025/1/12")

enum LCD_DEVELOPER_ITEM_INDEX
{
#define LCD_DEVELOPER_X(name, string) name
    LCD_DEVELOPER_ITEM_LIST,
#undef LCD_DEVELOPER_X
    LCD_DEVELOPER_ITEM_NUM
};

void lcd_developer_setup_init(void);
void lcd_developer_touch_cb(lv_event_t *e);

void lcd_developer_change_label_txt(enum LCD_DEVELOPER_ITEM_INDEX _index, char *_string);
void lcd_developer_change_label_txt_os(enum LCD_DEVELOPER_ITEM_INDEX _index, char *_string); // 修改显示信息

/*LCD PARAMETER*/

#define LCD_PARAMETER_ITEM_LIST                                       \
    LCD_PARAMETER_X(LIST_ITEM1, "揭盖机类型", "双水位单泵"),          \
        LCD_PARAMETER_X(LIST_ITEM2, "进水阀延时进水时间", "2秒"),     \
        LCD_PARAMETER_X(LIST_ITEM3, "洗涤门槛温度", "10度"),          \
        LCD_PARAMETER_X(LIST_ITEM4, "洗涤控温回差温度", "10度"),      \
        LCD_PARAMETER_X(LIST_ITEM5, "漂洗控温回差温度", "2度"),       \
        LCD_PARAMETER_X(LIST_ITEM6, "漂洗进水降温", "2度"),           \
        LCD_PARAMETER_X(LIST_ITEM7, "进水降温时间", "97度"),          \
        LCD_PARAMETER_X(LIST_ITEM8, "洗涤剂工作时间", "5秒"),         \
        LCD_PARAMETER_X(LIST_ITEM9, "洗涤剂强度", "5秒"),             \
        LCD_PARAMETER_X(LIST_ITEM10, "干燥剂工作时间", "18V"),        \
        LCD_PARAMETER_X(LIST_ITEM11, "干燥剂强度", "10秒"),           \
        LCD_PARAMETER_X(LIST_ITEM12, "漂洗工作时间", "18V"),          \
        LCD_PARAMETER_X(LIST_ITEM13, "泵抽水延时", "10秒"),           \
        LCD_PARAMETER_X(LIST_ITEM14, "进水超时", "不检测"),           \
        LCD_PARAMETER_X(LIST_ITEM15, "自动关机延时", "不关机"),       \
        LCD_PARAMETER_X(LIST_ITEM16, "节能时间", "40秒"),             \
        LCD_PARAMETER_X(LIST_ITEM17, "标准时间", "70秒"),             \
        LCD_PARAMETER_X(LIST_ITEM18, "强力时间", "100秒"),            \
        LCD_PARAMETER_X(LIST_ITEM19, "加热错峰开关", "关闭"),         \
        LCD_PARAMETER_X(LIST_ITEM20, "进水阀漂洗泵联动开关", "关闭"), \
        LCD_PARAMETER_X(LIST_ITEM21, "缺水暂停运行开关", "关闭"),     \
        LCD_PARAMETER_X(LIST_ITEM22, "运行前水满检查开关", "关闭"),   \
        LCD_PARAMETER_X(LIST_ITEM23, "漂洗强制进水开关", "关闭"),     \
        LCD_PARAMETER_X(LIST_ITEM24, "洗涤强制关水开关", "关闭"),     \
        LCD_PARAMETER_X(LIST_ITEM25, "机门打开强制进水阀关闭", "关闭")

enum LCD_PARAMETER_ITEM_INDEX
{
#define LCD_PARAMETER_X(name, param, value) name
    LCD_PARAMETER_ITEM_LIST,
#undef LCD_PARAMETER_X
    LCD_PARAMETER_ITEM_NUM
};

void lcd_parameter_setup_init(void);
void lcd_parameter_touch_cb(lv_event_t *e);

void lcd_parameter_choose_param_value(int8_t _index);
void lcd_parameter_change_param_item(uint8_t _index, char *_string);
void lcd_parameter_change_value_item(uint8_t _index, char *_string);

void lcd_parameter_choose_param_value_os(int8_t _index);                // 光标位置
void lcd_parameter_change_param_item_os(uint8_t _index, char *_string); // 改变参数名
void lcd_parameter_change_value_item_os(uint8_t _index, char *_string); // 改变参数值

/*LCD RUN*/
enum LCD_RUN_WATER_LEVEL
{
    LCD_RUN_WATER_LOW,
    LCD_RUN_WATER_NORMAL,
    LCD_RUN_WATER_FULL,
    LCD_RUN_WATERLEVEL_NUM
};

enum LCD_RUN_WORK_MODE
{
    LCD_RUN_ECO_MODE,
    LCD_RUN_NORMAL_MODE,
    LCD_RUN_TURBO_MODE,
    LCD_RUN_WORKMODE_NUM
};

#define LCD_RUN_ITEM_LIST                     \
    LCD_RUN_X(LCD_RUN_WORK_MODE),             \
        LCD_RUN_X(LCD_RUN_RINSE_TEMPERATURE), \
        LCD_RUN_X(LCD_RUN_WASH_TEMPERATURE),  \
        LCD_RUN_X(LCD_RUN_WASH_WATER_LEVEL),  \
        LCD_RUN_X(LCD_RUN_RINSE_WATER_LEVEL)

enum LCD_RUN_ITEM_INDEX
{
    LCD_RUN_ITEM_NONE,
#define LCD_RUN_X(name) name##_INDEX
    LCD_RUN_ITEM_LIST,
#undef LCD_RUN_X
    LCD_RUN_ITEM_NUM
};

enum LCD_RUN_ITEM_BITMASK
{
#define LCD_RUN_X(name) name##_BITMASK = 1 << name##_INDEX
    LCD_RUN_ITEM_LIST,
#undef LCD_RUN_X
    LCD_RUN_ALL_BITMASK = (1 << LCD_RUN_ITEM_NUM) - 1
};

void lcd_run_setup_init(void);
void lcd_run_touch_cb(lv_event_t *e);

void lcd_run_choose_workmode(enum LCD_RUN_WORK_MODE _workmode);
void lcd_run_choose_focus_obj(enum LCD_RUN_ITEM_INDEX _maskbit);
void lcd_run_add_focus(enum LCD_RUN_ITEM_BITMASK _maskbit);
void lcd_run_clear_focus(enum LCD_RUN_ITEM_BITMASK _maskbit);
uint16_t lcd_run_get_focusbit(void);
void lcd_run_set_wash_water_level(enum LCD_RUN_WATER_LEVEL _level);
void lcd_run_set_rinse_water_level(enum LCD_RUN_WATER_LEVEL _level);
void lcd_run_set_wash_preset_temperature(int16_t _temperature);
void lcd_run_set_wash_actual_temperature(int16_t _temperature);
void lcd_run_set_rinse_preset_temperature(int16_t _temperature);
void lcd_run_set_rinse_actual_temperature(int16_t _temperature);
void lcd_run_set_progress(uint16_t _num);
void lcd_run_show_warning(char *_string);
void lcd_run_hide_warning(void);
void lcd_run_setdoor_state(uint8_t _onoff);

void lcd_run_choose_workmode_os(enum LCD_RUN_WORK_MODE _workmode);      // 工作模式光标（光标2）
uint16_t lcd_run_get_workmode(void);                                    // 需要获取光标2
void lcd_run_choose_focus_obj_os(enum LCD_RUN_ITEM_INDEX _index);       // 光标1
void lcd_run_add_focus_os(enum LCD_RUN_ITEM_BITMASK _maskbit);          // 添加光标1（不会用）
void lcd_run_clear_focus_os(enum LCD_RUN_ITEM_BITMASK _maskbit);        // 删除光标1（不会用）
uint16_t lcd_run_get_focusbit_os(void);                                 // 获取当前光标1
void lcd_run_set_wash_water_level_os(enum LCD_RUN_WATER_LEVEL _level);  // 设置漂洗水位
uint16_t lcd_run_get_wash_water_levelbit_os(void);                      // 需要获取漂洗水位
void lcd_run_set_rinse_water_level_os(enum LCD_RUN_WATER_LEVEL _level); // 设置洗涤水位
// 需要洗涤水位
void lcd_run_set_wash_preset_temperature_os(int16_t _temperature);  // 漂洗设定温度
void lcd_run_set_wash_actual_temperature_os(int16_t _temperature);  // 漂洗实际温度
void lcd_run_set_rinse_preset_temperature_os(int16_t _temperature); // 洗涤设定温度
void lcd_run_set_rinse_actual_temperature_os(int16_t _temperature); // 洗涤实际温度
void lcd_run_set_progress_os(uint8_t _num);                         // 设置进度条
void lcd_run_show_warning_os(char *_string);                        // 显示警告
void lcd_run_hide_warning_os(void);                                 // 隐藏警告
void lcd_run_setdoor_state_os(uint8_t _onoff);                      // 设置机门状态

/*LCD SETTTNG*/

/*修改UI界面后得把这里同步修改 */
#define LCD_SETTING_PARENT_PARAM_LIST             \
    LCD_SETTING_X(LCD_SETTING_NTC_SIGNAL),        \
        LCD_SETTING_X(LCD_SETTING_SWITCH_SIGNAL), \
        LCD_SETTING_X(LCD_SETTING_220V),          \
        LCD_SETTING_X(LCD_SETTING_24V)

enum LCD_SETTING_PARENT_PARAM_INDEX
{
#define LCD_SETTING_X(name) name
    LCD_SETTING_PARENT_PARAM_LIST,
#undef LCD_SETTING_X
    LCD_SETTING_PARENT_PARAM_NUM
};

/*需要保证字符串长度小于UI_STING_LENGTH*/
#define LCD_SETTING_SUB_PARAM_LIST                                                    \
    LCD_SETTING_X(LCD_SETTING_NTC_SIGNAL, LCD_SETTING_NTC1, "#NTC1:洗涤温度"),        \
        LCD_SETTING_X(LCD_SETTING_NTC_SIGNAL, LCD_SETTING_NTC2, "#NTC2:漂洗温度"),    \
        LCD_SETTING_X(LCD_SETTING_NTC_SIGNAL, LCD_SETTING_NTC3, "#NTC3:闲置"),        \
        LCD_SETTING_X(LCD_SETTING_SWITCH_SIGNAL, LCD_SETTING_SWITCH1, "#信号1:洗高"), \
        LCD_SETTING_X(LCD_SETTING_SWITCH_SIGNAL, LCD_SETTING_SWITCH2, "#信号2:洗低"), \
        LCD_SETTING_X(LCD_SETTING_SWITCH_SIGNAL, LCD_SETTING_SWITCH3, "#信号3:漂高"), \
        LCD_SETTING_X(LCD_SETTING_SWITCH_SIGNAL, LCD_SETTING_SWITCH4, "#信号4:漂低"), \
        LCD_SETTING_X(LCD_SETTING_SWITCH_SIGNAL, LCD_SETTING_SWITCH5, "#信号5:机门"), \
        LCD_SETTING_X(LCD_SETTING_SWITCH_SIGNAL, LCD_SETTING_SWITCH6, "#信号6:闲置"), \
        LCD_SETTING_X(LCD_SETTING_220V, LCD_SETTING_220V_1, "#继电器1:洗涤剂"),       \
        LCD_SETTING_X(LCD_SETTING_220V, LCD_SETTING_220V_2, "#继电器2:干燥机"),       \
        LCD_SETTING_X(LCD_SETTING_220V, LCD_SETTING_220V_3, "#继电器3:进水阀"),       \
        LCD_SETTING_X(LCD_SETTING_220V, LCD_SETTING_220V_4, "#继电器4:洗加热"),       \
        LCD_SETTING_X(LCD_SETTING_220V, LCD_SETTING_220V_5, "#继电器5:漂加热"),       \
        LCD_SETTING_X(LCD_SETTING_220V, LCD_SETTING_220V_6, "#继电器6:洗涤泵"),       \
        LCD_SETTING_X(LCD_SETTING_220V, LCD_SETTING_220V_7, "#继电器7:漂洗泵"),       \
        LCD_SETTING_X(LCD_SETTING_220V, LCD_SETTING_220V_8, "#继电器8:闲置"),         \
        LCD_SETTING_X(LCD_SETTING_220V, LCD_SETTING_220V_9, "#继电器9:闲置"),         \
        LCD_SETTING_X(LCD_SETTING_24V, LCD_SETTING_24V_1, "#24V输出1:洗涤剂"),        \
        LCD_SETTING_X(LCD_SETTING_24V, LCD_SETTING_24V_2, "#24V输出2:干燥机")

enum LCD_SETTING_SUB_PARAM_INDEX
{
#define LCD_SETTING_X(parent, name, string) name
    LCD_SETTING_SUB_PARAM_LIST,
#undef LCD_SETTING_X
    LCD_SETTING_SUB_PARAM_NUM
};

void lcd_setting_setup_init(void);
void lcd_setting_touch_cb(lv_event_t *e);

void lcd_setting_choose_param(enum LCD_SETTING_SUB_PARAM_INDEX _typeindex);
void lcd_setting_change_current_value(char *_string);
void lcd_setting_bind(void);
void lcd_settting_change_param_string(enum LCD_SETTING_SUB_PARAM_INDEX _index, char *_string);

void lcd_setting_choose_param_os(enum LCD_SETTING_SUB_PARAM_INDEX _typeindex);                    // 光标位置
void lcd_setting_change_current_value_os(char *_string);                                          // 参数值
void lcd_setting_bind_os(void);                                                                   // 将参数绑定
void lcd_settting_change_param_string_os(enum LCD_SETTING_SUB_PARAM_INDEX _index, char *_string); // 修改参数

/*LCD STANDBY*/
enum LCD_STANDBY_ROLLER_INDEX
{
    LCD_STANDBY_ROLLER_INDEX_RUNNING,
    LCD_STANDBY_ROLLER_INDEX_PARAM_CONFIG,
    LCD_STANDBY_ROLLER_INDEX_HARDWARE_MATCH,
    LCD_STANDBY_ROLLER_INDEX_SYSTEM_ADJUST,
    LCD_STANDBY_NUM
};

void lcd_standby_setup_init(void);
void roller_value_change_cb(uint8_t _index);

void lcd_standby_set_roller_index(enum LCD_STANDBY_ROLLER_INDEX _index);
uint8_t lcd_standby_get_roller_index(void);

void lcd_standby_set_roller_index_os(enum LCD_STANDBY_ROLLER_INDEX _index); // 改变滚动条，从0开始
uint8_t lcd_standby_get_roller_index_os(void);                              // 获取滚动条

/*LCD START*/
void lcd_start_setup_init(void);
void lcd_start_touch_cb(lv_event_t *e);

void ui_init(void);
#endif
