/*
 * Copyright 2025 NXP
 * NXP Proprietary. This software is owned or controlled by NXP and may only be used strictly in
 * accordance with the applicable license terms. By expressly accepting such terms or by downloading, installing,
 * activating and/or otherwise using the software, you are agreeing that you have read, and that you agree to
 * comply with and are bound by, such license terms.  If you do not agree to be bound by the applicable license
 * terms, then you may not retain, install, activate or otherwise use the software.
 */

#include "lvgl.h"
#include "gui_guider.h"
#include "widgets_init.h"
#include <stdlib.h>
#include "aic_ui.h"
__attribute__((unused)) void kb_event_cb(lv_event_t *e)
{
    lv_event_code_t code = lv_event_get_code(e);
    lv_obj_t *kb = lv_event_get_target(e);
    if (code == LV_EVENT_READY || code == LV_EVENT_CANCEL)
    {
        lv_obj_add_flag(kb, LV_OBJ_FLAG_HIDDEN);
    }
}

__attribute__((unused)) void ta_event_cb(lv_event_t *e)
{
    lv_event_code_t code = lv_event_get_code(e);
#if LV_USE_KEYBOARD || LV_USE_ZH_KEYBOARD
    lv_obj_t *ta = lv_event_get_target(e);
#endif
    lv_obj_t *kb = lv_event_get_user_data(e);
    if (code == LV_EVENT_FOCUSED || code == LV_EVENT_CLICKED)
    {
#if LV_USE_ZH_KEYBOARD != 0
        lv_zh_keyboard_set_textarea(kb, ta);
#endif
#if LV_USE_KEYBOARD != 0
        lv_keyboard_set_textarea(kb, ta);
#endif
        lv_obj_move_foreground(kb);
        lv_obj_clear_flag(kb, LV_OBJ_FLAG_HIDDEN);
    }
    if (code == LV_EVENT_CANCEL || code == LV_EVENT_DEFOCUSED)
    {

#if LV_USE_ZH_KEYBOARD != 0
        lv_zh_keyboard_set_textarea(kb, ta);
#endif
#if LV_USE_KEYBOARD != 0
        lv_keyboard_set_textarea(kb, ta);
#endif
        lv_obj_move_background(kb);
        lv_obj_add_flag(kb, LV_OBJ_FLAG_HIDDEN);
    }
}

#if LV_USE_ANALOGCLOCK != 0
void clock_count(int *hour, int *min, int *sec)
{
    (*sec)++;
    if (*sec == 60)
    {
        *sec = 0;
        (*min)++;
    }
    if (*min == 60)
    {
        *min = 0;
        if (*hour < 12)
        {
            (*hour)++;
        }
        else
        {
            (*hour)++;
            *hour = *hour % 12;
        }
    }
}
#endif

const char *lcd_standby_animimg_1_imgs[12] = {
#if LV_USE_GUIDER_SIMULATOR
    "C:\\Users\\<USER>\\Desktop\\dishwasher(1)\\dishwasher\\import\\image\\27.png",
    "C:\\Users\\<USER>\\Desktop\\dishwasher(1)\\dishwasher\\import\\image\\28.png",
    "C:\\Users\\<USER>\\Desktop\\dishwasher(1)\\dishwasher\\import\\image\\29.png",
    "C:\\Users\\<USER>\\Desktop\\dishwasher(1)\\dishwasher\\import\\image\\30.png",
    "C:\\Users\\<USER>\\Desktop\\dishwasher(1)\\dishwasher\\import\\image\\31.png",
    "C:\\Users\\<USER>\\Desktop\\dishwasher(1)\\dishwasher\\import\\image\\32.png",
    "C:\\Users\\<USER>\\Desktop\\dishwasher(1)\\dishwasher\\import\\image\\33.png",
    "C:\\Users\\<USER>\\Desktop\\dishwasher(1)\\dishwasher\\import\\image\\34.png",
    "C:\\Users\\<USER>\\Desktop\\dishwasher(1)\\dishwasher\\import\\image\\35.png",
    "C:\\Users\\<USER>\\Desktop\\dishwasher(1)\\dishwasher\\import\\image\\36.png",
    "C:\\Users\\<USER>\\Desktop\\dishwasher(1)\\dishwasher\\import\\image\\37.png",
    "C:\\Users\\<USER>\\Desktop\\dishwasher(1)\\dishwasher\\import\\image\\38.png",
#else

    LVGL_PATH(27.png),
    LVGL_PATH(28.png),
    LVGL_PATH(29.png),
    LVGL_PATH(30.png),
    LVGL_PATH(31.png),
    LVGL_PATH(32.png),
    LVGL_PATH(33.png),
    LVGL_PATH(34.png),
    LVGL_PATH(35.png),
    LVGL_PATH(36.png),
    LVGL_PATH(37.png),
    LVGL_PATH(38.png)
#endif

};
const char *lcd_run_run_animimg_imgs[12] = {
#if LV_USE_GUIDER_SIMULATOR
    "C:\\Users\\<USER>\\Desktop\\dishwasher(1)\\dishwasher\\import\\image\\27.png",
    "C:\\Users\\<USER>\\Desktop\\dishwasher(1)\\dishwasher\\import\\image\\28.png",
    "C:\\Users\\<USER>\\Desktop\\dishwasher(1)\\dishwasher\\import\\image\\29.png",
    "C:\\Users\\<USER>\\Desktop\\dishwasher(1)\\dishwasher\\import\\image\\30.png",
    "C:\\Users\\<USER>\\Desktop\\dishwasher(1)\\dishwasher\\import\\image\\31.png",
    "C:\\Users\\<USER>\\Desktop\\dishwasher(1)\\dishwasher\\import\\image\\32.png",
    "C:\\Users\\<USER>\\Desktop\\dishwasher(1)\\dishwasher\\import\\image\\33.png",
    "C:\\Users\\<USER>\\Desktop\\dishwasher(1)\\dishwasher\\import\\image\\34.png",
    "C:\\Users\\<USER>\\Desktop\\dishwasher(1)\\dishwasher\\import\\image\\35.png",
    "C:\\Users\\<USER>\\Desktop\\dishwasher(1)\\dishwasher\\import\\image\\36.png",
    "C:\\Users\\<USER>\\Desktop\\dishwasher(1)\\dishwasher\\import\\image\\37.png",
    "C:\\Users\\<USER>\\Desktop\\dishwasher(1)\\dishwasher\\import\\image\\38.png",
#else

    LVGL_PATH(27.png),
    LVGL_PATH(28.png),
    LVGL_PATH(29.png),
    LVGL_PATH(30.png),
    LVGL_PATH(31.png),
    LVGL_PATH(32.png),
    LVGL_PATH(33.png),
    LVGL_PATH(34.png),
    LVGL_PATH(35.png),
    LVGL_PATH(36.png),
    LVGL_PATH(37.png),
    LVGL_PATH(38.png)
#endif

};
