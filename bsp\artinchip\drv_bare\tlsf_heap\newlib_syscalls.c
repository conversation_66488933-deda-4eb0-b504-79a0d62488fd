/*
 * Copyright (c) 2006-2021, RT-Thread Development Team
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Change Logs:
 * Date           Author       Notes
 * 2021-02-11     Meco Man     remove _gettimeofday_r() and _times_r()
 * 2021-02-13     Meco Man     re-implement exit() and abort()
 * 2021-02-21     Meco Man     improve and beautify syscalls
 * 2021-02-24     Meco Man     fix bug of _isatty_r()
 */

#include <reent.h>
#include <stdio.h>
#include <string.h>
#include <stddef.h>
#include <fcntl.h>
#include <unistd.h>
#include <sys/errno.h>
#include <sys/stat.h>
#include <aic_common.h>
#include <aic_tlsf.h>
#ifdef KERNEL_FREERTOS
#include <FreeRTOS.h>
#endif

void *_malloc_r(struct _reent *ptr, size_t size)
{
    void* result;

#if defined(KERNEL_BAREMETAL) || defined(KERNEL_UCOS_II)
    result = (void*)aic_tlsf_malloc(MEM_DEFAULT, size);
#elif defined(KERNEL_FREERTOS)
    result = pvPortMalloc(size);
#else
    #error unknown kernel
#endif
    if (result == NULL)
    {
        ptr->_errno = ENOMEM;
    }

    return result;
}

void *_realloc_r(struct _reent *ptr, void *old, size_t newlen)
{
    void* result;

#if defined(KERNEL_BAREMETAL) || defined(KERNEL_UCOS_II)
    result = (void*)aic_tlsf_realloc(MEM_DEFAULT, old, newlen);
#elif defined(KERNEL_FREERTOS)
    if (old)
        vPortFree(old);
    result = pvPortMalloc(newlen);
#else
    #error unknown kernel
#endif
    if (result == NULL)
    {
        ptr->_errno = ENOMEM;
    }

    return result;
}

void *_calloc_r(struct _reent *ptr, size_t size, size_t len)
{
    void* result;

#if defined(KERNEL_BAREMETAL) || defined(KERNEL_UCOS_II)
    result = (void*)aic_tlsf_calloc(MEM_DEFAULT, size, len);
#elif defined(KERNEL_FREERTOS)
    result = pvPortMalloc(size*len);
    if (result != NULL)
        /* clean memory */
        memset(result, 0, size*len);
#else
    #error unknown kernel
#endif
    if (result == NULL)
    {
        ptr->_errno = ENOMEM;
    }

    return result;
}

void _free_r(struct _reent *ptr, void *addr)
{
#if defined(KERNEL_BAREMETAL) || defined(KERNEL_UCOS_II)
    aic_tlsf_free(MEM_DEFAULT, addr);
#elif defined(KERNEL_FREERTOS)
    vPortFree(addr);
#else
    #error unknown kernel
#endif
}

int *__errno(void)
{
    /* There is no OS in Bootloader, only need one errno */
    static int bl_errno;
    return &bl_errno;
}

int _getpid_r(struct _reent *ptr)
{
    return 0;
}

int _close_r(struct _reent *ptr, int fd)
{
    ptr->_errno = ENOTSUP;
    return -1;
}

int _execve_r(struct _reent *ptr, const char * name, char *const *argv, char *const *env)
{
    ptr->_errno = ENOTSUP;
    return -1;
}

int _fcntl_r(struct _reent *ptr, int fd, int cmd, int arg)
{
    ptr->_errno = ENOTSUP;
    return -1;
}

int _fork_r(struct _reent *ptr)
{
    ptr->_errno = ENOTSUP;
    return -1;
}

int _fstat_r(struct _reent *ptr, int fd, struct stat *pstat)
{
    ptr->_errno = ENOTSUP;
    return -1;
}

int _isatty_r(struct _reent *ptr, int fd)
{
    if (fd >=0 && fd < 3)
    {
        return 1;
    }
    else
    {
        return 0;
    }
}

int _kill_r(struct _reent *ptr, int pid, int sig)
{
    ptr->_errno = ENOTSUP;
    return -1;
}

int _link_r(struct _reent *ptr, const char *old, const char *new)
{
    ptr->_errno = ENOTSUP;
    return -1;
}

int _wait_r(struct _reent *ptr, int *status)
{
    ptr->_errno = ENOTSUP;
    return -1;
}

mode_t umask(mode_t mask)
{
    return 022;
}

int flock(int fd, int operation)
{
    return 0;
}

_off_t _lseek_r(struct _reent *ptr, int fd, _off_t pos, int whence)
{
    ptr->_errno = ENOTSUP;
    return -1;
}

int _mkdir_r(struct _reent *ptr, const char *name, int mode)
{
    ptr->_errno = ENOTSUP;
    return -1;
}

int _open_r(struct _reent *ptr, const char *file, int flags, int mode)
{
    ptr->_errno = ENOTSUP;
    return -1;
}

_ssize_t _read_r(struct _reent *ptr, int fd, void *buf, size_t nbytes)
{
    ptr->_errno = ENOTSUP;
    return -1;
}

int _rename_r(struct _reent *ptr, const char *old, const char *new)
{
    ptr->_errno = ENOTSUP;
    return -1;
}

int _stat_r(struct _reent *ptr, const char *file, struct stat *pstat)
{
    ptr->_errno = ENOTSUP;
    return -1;
}

int _unlink_r(struct _reent *ptr, const char *file)
{
    ptr->_errno = ENOTSUP;
    return -1;
}

_ssize_t _write_r(struct _reent *ptr, int fd, const void *buf, size_t nbytes)
{
    ptr->_errno = ENOTSUP;
    return -1;
}

/* for exit() and abort() */
__attribute__ ((noreturn)) void _exit (int status)
{
    while(1);
}

/*
These functions are implemented and replaced by the 'common/time.c' file
int _gettimeofday_r(struct _reent *ptr, struct timeval *__tp, void *__tzp);
_CLOCK_T_  _times_r(struct _reent *ptr, struct tms *ptms);
*/
