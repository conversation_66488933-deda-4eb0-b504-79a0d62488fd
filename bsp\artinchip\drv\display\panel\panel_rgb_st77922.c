/*
 * Copyright (c) 2024, ArtInChip Technology Co., Ltd
 *
 * SPDX-License-Identifier: Apache-2.0
 */

#include "panel_com.h"

#define ST77922_RESET      "PD.3"

#define ST77922_CS         "PC.6"
#define ST77922_SCL        "PC.7"
#define ST77922_SDI        "PC.5"

static struct gpio_desc reset_gpio;

static void panel_gpio_init(void)
{
    panel_get_gpio(&reset_gpio, ST77922_RESET);

    panel_gpio_set_value(&reset_gpio, 1);
    aic_delay_ms(100);
    panel_gpio_set_value(&reset_gpio, 0);
    aic_delay_ms(100);
    panel_gpio_set_value(&reset_gpio, 1);
    aic_delay_ms(100);
}

static int panel_enable(struct aic_panel *panel)
{
    panel_gpio_init();

    panel_spi_device_emulation(ST77922_CS, ST77922_SDI, ST77922_SCL);

    panel_spi_send_seq(0x28);
    panel_spi_send_seq(0x10);
    aic_delay_ms(120);
    panel_spi_send_seq(0xD0, 0x02);

    /* ======================CMD2====================== */
    panel_spi_send_seq(0xF1, 0x00);
    panel_spi_send_seq(0x60, 0x00, 0x00, 0x00);
    panel_spi_send_seq(0x65, 0x80);
    panel_spi_send_seq(0x66, 0x02, 0x3F);
    panel_spi_send_seq(0xBE, 0x24, 0x00, 0xED);
    /* VFP, VBP, Gate line */
    panel_spi_send_seq(0x70, 0x11, 0x9D, 0x11, 0xE0, 0xE0, 0x00, 0x08, 0x75,
                       0x00, 0x00, 0x00, 0x1A);
    /* video mode */
    panel_spi_send_seq(0x71, 0xD3);
    panel_spi_send_seq(0x7B, 0x00, 0x08, 0x08);
    panel_spi_send_seq(0x80, 0x55, 0x62, 0x2F, 0x17, 0xF0, 0x52, 0x70, 0xD2,
                       0x52, 0x62, 0xEA);
    panel_spi_send_seq(0x81, 0x26, 0x52, 0x72, 0x27);
    panel_spi_send_seq(0x84, 0x92, 0x25);
    panel_spi_send_seq(0x86, 0xC6, 0x04, 0xB1, 0x02, 0x58, 0x12, 0x58, 0x10,
                       0x13, 0x01, 0xA5, 0x00, 0xA5, 0xA5);
    panel_spi_send_seq(0x87, 0x10, 0x10, 0x58, 0x00, 0x02, 0x3A);
    panel_spi_send_seq(0x88, 0x00, 0x00, 0x2C, 0x10, 0x04, 0x00, 0x00, 0x00,
                       0x01, 0x01, 0x01, 0x01, 0x01, 0x00, 0x06);
    panel_spi_send_seq(0x89, 0x00, 0x00, 0x00);
    panel_spi_send_seq(0x8A, 0x13, 0x00, 0x2C, 0x00, 0x00, 0x2C, 0x10, 0x10,
                       0x00, 0x3E, 0x19);
    /* VGL Pump */
    panel_spi_send_seq(0x8B, 0x15, 0xB1, 0xB1, 0x44, 0x96, 0x2C, 0x10, 0x97,
                       0x8E);
    /* VGH Pump */
    panel_spi_send_seq(0x8C, 0x1D, 0xB1, 0xB1, 0x44, 0x96, 0x2C, 0x10, 0x50,
                       0x0F, 0x01, 0xC5, 0x12, 0x09);
    panel_spi_send_seq(0x8D, 0x0C);
    panel_spi_send_seq(0x8E, 0x33, 0x01, 0x0C, 0x13, 0x01, 0x01);

    panel_spi_send_seq(0x90, 0x00, 0x44, 0x55, 0x7A, 0x00, 0x40, 0x40, 0x3F,
                       0x3F);
    panel_spi_send_seq(0x91, 0x00, 0x44, 0x55, 0x7B, 0x00, 0x40, 0x7F, 0x3F,
                       0x3F);
    panel_spi_send_seq(0x92, 0x00, 0x44, 0x55, 0x2F, 0x00, 0x30, 0x00, 0x05,
                       0x3F, 0x3F);
    panel_spi_send_seq(0x93, 0x00, 0x43, 0x11, 0x3F, 0x00, 0x3F, 0x00, 0x05,
                       0x3F, 0x3F);
    panel_spi_send_seq(0x94, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00);

    panel_spi_send_seq(0x95, 0x9D, 0x1D, 0x00, 0x00, 0xFF);
    panel_spi_send_seq(0x96, 0x44, 0x44, 0x07, 0x16, 0x3A, 0x3B, 0x01, 0x00,
                       0x3F, 0x3F, 0x00, 0x40);
    panel_spi_send_seq(0x97, 0x44, 0x44, 0x25, 0x34, 0x3C, 0x3D, 0x1F, 0x1E,
                       0x3F, 0x3F, 0x00, 0x40);
    panel_spi_send_seq(0xBA, 0x55, 0x3F, 0x3F, 0x3F, 0x3F);

    panel_spi_send_seq(0x9A, 0x40, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00);
    panel_spi_send_seq(0x9B, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00);

    panel_spi_send_seq(0x9C, 0x40, 0x12, 0x00, 0x00, 0x00, 0x12, 0x00, 0x00,
                       0x00, 0x12, 0x00, 0x00, 0x00);
    panel_spi_send_seq(0x9D, 0x80, 0x53, 0x00, 0x00, 0x00, 0x80, 0x64, 0x01);
    panel_spi_send_seq(0x9E, 0x53, 0x00, 0x00, 0x00, 0x80, 0x64, 0x01);

    panel_spi_send_seq(0x9F, 0xA0, 0x09, 0x00, 0x57);

    panel_spi_send_seq(0xB3, 0x00, 0x30, 0x0F, 0x00, 0x00, 0x00, 0x00);

    panel_spi_send_seq(0xB4, 0x10, 0x09, 0x0B, 0x02, 0x00, 0x19, 0x18, 0x13,
                       0x1E, 0x1D, 0x1C, 0x1E);
    panel_spi_send_seq(0xB5, 0x08, 0x12, 0x03, 0x0A, 0x19, 0x01, 0x11, 0x18,
                       0x1D, 0x1E, 0x1E, 0x1C);
    panel_spi_send_seq(0xB6, 0xFF, 0xFF, 0x00, 0x07, 0xFF, 0x0B, 0xFF);
    /* GammaP */
    panel_spi_send_seq(0xB7, 0x00, 0x0B, 0x12, 0x0A, 0x0B, 0x06, 0x37, 0x00,
                       0x02, 0x4D, 0x08, 0x14, 0x14, 0x30, 0x36, 0x0F);
    /* GammaN */
    panel_spi_send_seq(0xB8, 0x00, 0x0B, 0x11, 0x09, 0x09, 0x06, 0x37, 0x06,
                       0x05, 0x4D, 0x08, 0x13, 0x13, 0x2F, 0x36, 0x0F);

    panel_spi_send_seq(0xB9, 0x23, 0x23);
    panel_spi_send_seq(0xBB, 0x00, 0x00);
    /* VGHP/VGLP */
    panel_spi_send_seq(0xBF, 0x0F, 0x13, 0x13, 0x09, 0x09, 0x09);

    /* ======================CMD3====================== */
    panel_spi_send_seq(0xF2, 0x00);
    /* VOP = 5v */
    panel_spi_send_seq(0x73, 0x04, 0xBA, 0x12, 0x5E, 0x55);
    panel_spi_send_seq(0x77, 0x6B, 0x5B, 0xFD, 0xC3, 0xC5);
    panel_spi_send_seq(0x7A, 0x15, 0x27);
    panel_spi_send_seq(0x7B, 0x04, 0x57);
    panel_spi_send_seq(0x7E, 0x01, 0x0E);
    panel_spi_send_seq(0xBF, 0x36);
    /* VMF */
    panel_spi_send_seq(0xE3, 0x40, 0x40);

    /* ======================CMD1====================== */
    panel_spi_send_seq(0xF0, 0x00);
    panel_spi_send_seq(0x21);
    panel_spi_send_seq(0x11);

    aic_delay_ms(120);
    panel_spi_send_seq(0x29);
    panel_spi_send_seq(0x35, 0x00);

    panel_di_enable(panel, 0);
    panel_de_timing_enable(panel, 0);
    panel_backlight_enable(panel, 0);
    return 0;
}

static struct aic_panel_funcs st77922_funcs = {
    .disable = panel_default_disable,
    .unprepare = panel_default_unprepare,
    .prepare = panel_default_prepare,
    .enable = panel_enable,
    .register_callback = panel_register_callback,
};

static struct display_timing st77922_timing = {
    .pixelclock = 20000000,
    .hactive = 480,
    .hfront_porch = 100,
    .hback_porch = 40,
    .hsync_len = 2,
    .vactive = 480,
    .vfront_porch = 117,
    .vback_porch = 6,
    .vsync_len = 2,
};

static struct panel_rgb rgb = {
    .mode = SRGB,
    .format = SRGB_8BIT,
    .clock_phase = DEGREE_0,
    .data_order = RGB,
    .data_mirror = 0,
};

struct aic_panel rgb_st77922 = {
    .name = "panel-st77922",
    .timings = &st77922_timing,
    .funcs = &st77922_funcs,
    .rgb = &rgb,
    .connector_type = AIC_RGB_COM,
};
