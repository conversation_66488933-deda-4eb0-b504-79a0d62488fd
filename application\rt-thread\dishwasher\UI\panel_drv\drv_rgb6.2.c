#include "drv_rgb6.2.h"
#include <rtthread.h>
#include <rtdevice.h>
#include "aic_core.h"
#include "aic_hal_clk.h"
#include "aic_hal_gpio.h"

//-----------------LCD端口移植----------------
#define LCD_IOVCC rt_pin_get("PA.8")
#define LCD_DI rt_pin_get("PA.11")
#define LCD_SCL rt_pin_get("PA.10")
#define LCD_CS rt_pin_get("PA.9")

//-----------------LCD端口定义----------------

#define LCD_IOVCC_Clr() rt_pin_write(LCD_IOVCC, 0) // RES
#define LCD_IOVCC_Set() rt_pin_write(LCD_IOVCC, 1)

#define LCD_DI_Clr() rt_pin_write(LCD_DI, 0) // DC
#define LCD_DI_Set() rt_pin_write(LCD_DI, 1)

#define LCD_SCL_Clr() rt_pin_write(LCD_SCL, 0) // BLK
#define LCD_SCL_Set() rt_pin_write(LCD_SCL, 1)

#define LCD_CS_Clr() rt_pin_write(LCD_CS, 0) // CS
#define LCD_CS_Set() rt_pin_write(LCD_CS, 1)

void delay_ms(int ms) { rt_thread_mdelay(ms); }
void delay_us(int us) { aic_udelay(us); }

// SPI写入数据
static void SPI_SendData(rt_uint8_t txData)
{
    rt_uint8_t i;
    LCD_SCL_Clr();
    delay_us(50);

    for (i = 0; i < 8; i++)
    {
        // 数据发送
        if (txData & 0x80)
        {
            LCD_DI_Set();
        }
        else
        {
            LCD_DI_Clr();
        }
        txData <<= 1;
        delay_us(50);
        LCD_SCL_Clr();
        delay_us(50);
        LCD_SCL_Set();
        delay_us(50);
    }
}

static void SPI_WriteComm_9BIT(rt_uint8_t Addr)
{
    LCD_CS_Clr();
    delay_us(50);

    LCD_DI_Clr();
    delay_us(50);

    LCD_SCL_Clr();
    delay_us(50);

    LCD_SCL_Set();
    delay_us(50);

    SPI_SendData(Addr);

    LCD_CS_Set();
}

static void SPI_WriteData_9BIT(rt_uint8_t Data1)
{
    LCD_CS_Clr();
    delay_us(50);

    LCD_DI_Set();
    delay_us(50);

    LCD_SCL_Clr();
    delay_us(50);

    LCD_SCL_Set();
    delay_us(50);

    SPI_SendData(Data1);

    LCD_CS_Set();
}

#define Write_LCD_REG(X)                                                                      \
    do                                                                                        \
    {                                                                                         \
        (X & 0x0100) ? SPI_WriteData_9BIT((rt_uint8_t)X) : SPI_WriteComm_9BIT((rt_uint8_t)X); \
    } while (0)

static void lcd_gpio_init(void)
{
    unsigned int g;
    unsigned int p;
    g = GPIO_GROUP(LCD_IOVCC);
    p = GPIO_GROUP_PIN(LCD_IOVCC);
    hal_gpio_set_drive_strength(g, p, 3);
    g = GPIO_GROUP(LCD_DI);
    p = GPIO_GROUP_PIN(LCD_DI);
    hal_gpio_set_drive_strength(g, p, 3);
    g = GPIO_GROUP(LCD_SCL);
    p = GPIO_GROUP_PIN(LCD_SCL);
    hal_gpio_set_drive_strength(g, p, 3);
    g = GPIO_GROUP(LCD_CS);
    p = GPIO_GROUP_PIN(LCD_CS);
    hal_gpio_set_drive_strength(g, p, 3);
    rt_pin_mode(LCD_IOVCC, PIN_MODE_OUTPUT); // 配置引脚为输出模式
    rt_pin_mode(LCD_DI, PIN_MODE_OUTPUT);    // 配置引脚为输出模式
    rt_pin_mode(LCD_SCL, PIN_MODE_OUTPUT);   // 配置引脚为输出模式
    rt_pin_mode(LCD_CS, PIN_MODE_OUTPUT);    // 配置引脚为输出模式

    LCD_IOVCC_Set();

    delay_ms(300);//d12此处需要延时,PA.8拉高电平需要一些时间,原因未知.
    LCD_DI_Clr();
    LCD_SCL_Clr();
    LCD_CS_Set();

    log_e("LCD_GPIO_Init end!!\n");
}

static int GC9503V_Init(void)
{
    lcd_gpio_init();
    
    //****************************************************************************//

#if 1 // 原来能用的（正扫）
    Write_LCD_REG(0x00F0);
    Write_LCD_REG(0x0155);
    Write_LCD_REG(0x01AA);
    Write_LCD_REG(0x0152);
    Write_LCD_REG(0x0108);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x00F6);
    Write_LCD_REG(0x015A);
    Write_LCD_REG(0x0187);
    Write_LCD_REG(0x00C1);
    Write_LCD_REG(0x013F);
    Write_LCD_REG(0x00CD);
    Write_LCD_REG(0x0125);
    Write_LCD_REG(0x00C9);
    Write_LCD_REG(0x0110);
    Write_LCD_REG(0x00F8);
    Write_LCD_REG(0x018A);
    Write_LCD_REG(0x00AC);
    Write_LCD_REG(0x0145);
    Write_LCD_REG(0x00A7);
    Write_LCD_REG(0x0147);
    Write_LCD_REG(0x00A0);
    Write_LCD_REG(0x01CC);
    Write_LCD_REG(0x0086);
    Write_LCD_REG(0x0199);
    Write_LCD_REG(0x01A3);
    Write_LCD_REG(0x01A3);
    Write_LCD_REG(0x0131);
    Write_LCD_REG(0x00FA);
    Write_LCD_REG(0x0108);
    Write_LCD_REG(0x0108);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x0104);
    Write_LCD_REG(0x00A3);
    Write_LCD_REG(0x016E);
    Write_LCD_REG(0x00FD);
    Write_LCD_REG(0x0128);
    Write_LCD_REG(0x013C);
    Write_LCD_REG(0x0100);

    Write_LCD_REG(0x009A);
    Write_LCD_REG(0x015a);
    Write_LCD_REG(0x009B);
    Write_LCD_REG(0x0122);
    Write_LCD_REG(0x0082);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x0100);

    Write_LCD_REG(0x0080);
    Write_LCD_REG(0x014a);

    Write_LCD_REG(0x00B1);
    // Write_LCD_REG(0x01B0);
    Write_LCD_REG(0x0130);
    Write_LCD_REG(0x007A);
    Write_LCD_REG(0x010F);
    Write_LCD_REG(0x0113);
    Write_LCD_REG(0x007B);
    Write_LCD_REG(0x010F);
    Write_LCD_REG(0x0113);
    Write_LCD_REG(0x006D);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x011e);
    Write_LCD_REG(0x0102);
    Write_LCD_REG(0x0108);
    Write_LCD_REG(0x011a);
    Write_LCD_REG(0x0119);
    Write_LCD_REG(0x010d);
    Write_LCD_REG(0x0111);
    Write_LCD_REG(0x0112);
    Write_LCD_REG(0x0113);
    Write_LCD_REG(0x0114);
    Write_LCD_REG(0x011E);
    Write_LCD_REG(0x011E);
    Write_LCD_REG(0x011E);
    Write_LCD_REG(0x011E);
    Write_LCD_REG(0x011E);
    Write_LCD_REG(0x011E);
    Write_LCD_REG(0x011E);
    Write_LCD_REG(0x011E);
    Write_LCD_REG(0x010c);
    Write_LCD_REG(0x010b);
    Write_LCD_REG(0x010a);
    Write_LCD_REG(0x0109);
    Write_LCD_REG(0x010d);
    Write_LCD_REG(0x0119);
    Write_LCD_REG(0x011a);
    Write_LCD_REG(0x0107);
    Write_LCD_REG(0x0101);
    Write_LCD_REG(0x011E);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x0064);
    Write_LCD_REG(0x0138);
    Write_LCD_REG(0x0104);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x01c4);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x0138);
    Write_LCD_REG(0x0102);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x01c6);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x012C);
    Write_LCD_REG(0x017A);
    Write_LCD_REG(0x012C);
    Write_LCD_REG(0x017A);
    Write_LCD_REG(0x0065);
    Write_LCD_REG(0x0138);
    Write_LCD_REG(0x0108);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x01c0);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x0138);
    Write_LCD_REG(0x0106);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x01c2);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x012C);
    Write_LCD_REG(0x017A);
    Write_LCD_REG(0x012C);
    Write_LCD_REG(0x017A);
    Write_LCD_REG(0x0066);
    Write_LCD_REG(0x0183);
    Write_LCD_REG(0x01d0);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x01c4);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x0183);
    Write_LCD_REG(0x01d0);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x01c4);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x012C);
    Write_LCD_REG(0x017A);
    Write_LCD_REG(0x012C);
    Write_LCD_REG(0x017A);
    Write_LCD_REG(0x0060);
    Write_LCD_REG(0x0138);
    Write_LCD_REG(0x010c);
    Write_LCD_REG(0x013c);
    Write_LCD_REG(0x013c);
    Write_LCD_REG(0x0138);
    Write_LCD_REG(0x010b);
    Write_LCD_REG(0x013c);
    Write_LCD_REG(0x013c);
    Write_LCD_REG(0x0061);
    Write_LCD_REG(0x01b3);
    Write_LCD_REG(0x01c4);
    Write_LCD_REG(0x013c);
    Write_LCD_REG(0x013c);
    Write_LCD_REG(0x01b3);
    Write_LCD_REG(0x01c4);
    Write_LCD_REG(0x013c);
    Write_LCD_REG(0x013c);
    Write_LCD_REG(0x0062);
    Write_LCD_REG(0x01b3);
    Write_LCD_REG(0x01c4);
    Write_LCD_REG(0x013c);
    Write_LCD_REG(0x013c);
    Write_LCD_REG(0x01b3);
    Write_LCD_REG(0x01c4);
    Write_LCD_REG(0x013c);
    Write_LCD_REG(0x013c);
    Write_LCD_REG(0x0063);
    Write_LCD_REG(0x0138);
    Write_LCD_REG(0x010a);
    Write_LCD_REG(0x013c);
    Write_LCD_REG(0x013c);
    Write_LCD_REG(0x0138);
    Write_LCD_REG(0x0109);
    Write_LCD_REG(0x013c);
    Write_LCD_REG(0x013c);
    Write_LCD_REG(0x0068);
    Write_LCD_REG(0x0177);
    Write_LCD_REG(0x0108);
    Write_LCD_REG(0x010a);
    Write_LCD_REG(0x0108);
    Write_LCD_REG(0x0109);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x0118);
    Write_LCD_REG(0x010a);
    Write_LCD_REG(0x0108);
    Write_LCD_REG(0x0109);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x0069);
    Write_LCD_REG(0x0114);
    Write_LCD_REG(0x0122);
    Write_LCD_REG(0x0114);
    Write_LCD_REG(0x0122);
    Write_LCD_REG(0x0144);
    Write_LCD_REG(0x0122);
    Write_LCD_REG(0x0108);
    Write_LCD_REG(0x006B);
    Write_LCD_REG(0x0107);
    Write_LCD_REG(0x00D1);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x0110);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x0122);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x012c);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x012e);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x0156);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x0158);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x017c);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x019a);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x01ce);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x01fa);
    Write_LCD_REG(0x0101);
    Write_LCD_REG(0x014c);
    Write_LCD_REG(0x0101);
    Write_LCD_REG(0x0194);
    Write_LCD_REG(0x0101);
    Write_LCD_REG(0x0196);
    Write_LCD_REG(0x0101);
    Write_LCD_REG(0x01da);
    Write_LCD_REG(0x0102);
    Write_LCD_REG(0x0132);
    Write_LCD_REG(0x0102);
    Write_LCD_REG(0x0176);
    Write_LCD_REG(0x0102);
    Write_LCD_REG(0x01cc);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x0118);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x0155);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x016b);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x019b);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x01ac);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x01b8);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x01e0);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x01FF);
    Write_LCD_REG(0x00D2);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x0110);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x0122);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x012c);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x012e);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x0156);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x0158);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x017c);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x019a);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x01ce);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x01fa);
    Write_LCD_REG(0x0101);
    Write_LCD_REG(0x014c);
    Write_LCD_REG(0x0101);
    Write_LCD_REG(0x0194);
    Write_LCD_REG(0x0101);
    Write_LCD_REG(0x0196);
    Write_LCD_REG(0x0101);
    Write_LCD_REG(0x01da);
    Write_LCD_REG(0x0102);
    Write_LCD_REG(0x0132);
    Write_LCD_REG(0x0102);
    Write_LCD_REG(0x0176);
    Write_LCD_REG(0x0102);
    Write_LCD_REG(0x01cc);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x0118);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x0155);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x016b);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x019b);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x01ac);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x01b8);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x01e0);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x01FF);
    Write_LCD_REG(0x00D3);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x0110);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x0122);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x012c);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x012e);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x0156);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x0158);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x017c);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x019a);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x01ce);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x01fa);
    Write_LCD_REG(0x0101);
    Write_LCD_REG(0x014c);
    Write_LCD_REG(0x0101);
    Write_LCD_REG(0x0194);
    Write_LCD_REG(0x0101);
    Write_LCD_REG(0x0196);
    Write_LCD_REG(0x0101);
    Write_LCD_REG(0x01da);
    Write_LCD_REG(0x0102);
    Write_LCD_REG(0x0132);
    Write_LCD_REG(0x0102);
    Write_LCD_REG(0x0176);
    Write_LCD_REG(0x0102);
    Write_LCD_REG(0x01cc);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x0118);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x0155);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x016b);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x019b);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x01ac);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x01b8);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x01e0);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x01FF);
    Write_LCD_REG(0x00D4);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x0110);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x0122);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x012c);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x012e);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x0156);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x0158);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x017c);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x019a);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x01ce);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x01fa);
    Write_LCD_REG(0x0101);
    Write_LCD_REG(0x014c);
    Write_LCD_REG(0x0101);
    Write_LCD_REG(0x0194);
    Write_LCD_REG(0x0101);
    Write_LCD_REG(0x0196);
    Write_LCD_REG(0x0101);
    Write_LCD_REG(0x01da);
    Write_LCD_REG(0x0102);
    Write_LCD_REG(0x0132);
    Write_LCD_REG(0x0102);
    Write_LCD_REG(0x0176);
    Write_LCD_REG(0x0102);
    Write_LCD_REG(0x01cc);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x0118);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x0155);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x016b);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x019b);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x01ac);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x01b8);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x01e0);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x01FF);
    Write_LCD_REG(0x00D5);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x0110);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x0122);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x012c);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x012e);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x0156);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x0158);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x017c);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x019a);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x01ce);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x01fa);
    Write_LCD_REG(0x0101);
    Write_LCD_REG(0x014c);
    Write_LCD_REG(0x0101);
    Write_LCD_REG(0x0194);
    Write_LCD_REG(0x0101);
    Write_LCD_REG(0x0196);
    Write_LCD_REG(0x0101);
    Write_LCD_REG(0x01da);
    Write_LCD_REG(0x0102);
    Write_LCD_REG(0x0132);
    Write_LCD_REG(0x0102);
    Write_LCD_REG(0x0176);
    Write_LCD_REG(0x0102);
    Write_LCD_REG(0x01cc);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x0118);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x0155);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x016b);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x019b);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x01ac);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x01b8);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x01e0);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x01FF);
    Write_LCD_REG(0x00D6);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x0110);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x0122);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x012c);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x012e);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x0156);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x0158);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x017c);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x019a);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x01ce);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x01fa);
    Write_LCD_REG(0x0101);
    Write_LCD_REG(0x014c);
    Write_LCD_REG(0x0101);
    Write_LCD_REG(0x0194);
    Write_LCD_REG(0x0101);
    Write_LCD_REG(0x0196);
    Write_LCD_REG(0x0101);
    Write_LCD_REG(0x01da);
    Write_LCD_REG(0x0102);
    Write_LCD_REG(0x0132);
    Write_LCD_REG(0x0102);
    Write_LCD_REG(0x0176);
    Write_LCD_REG(0x0102);
    Write_LCD_REG(0x01cc);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x0118);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x0155);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x016b);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x019b);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x01ac);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x01b8);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x01e0);
    Write_LCD_REG(0x0103);
    Write_LCD_REG(0x01FF);

    Write_LCD_REG(0x0011);
    delay_us(200000);

    Write_LCD_REG(0x0035);
    Write_LCD_REG(0x0100);
    Write_LCD_REG(0x0036);
    Write_LCD_REG(0x0108);
    Write_LCD_REG(0x003a);
    Write_LCD_REG(0x0177);

    Write_LCD_REG(0x0029);
    delay_us(20000);
    Write_LCD_REG(0x0011);
    delay_us(20000);
#endif
#if 0 // 反扫
Write_LCD_REG(0x00F0);
Write_LCD_REG(0x0155);
Write_LCD_REG(0x01AA);
Write_LCD_REG(0x0152);
Write_LCD_REG(0x0108);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x00F6);
Write_LCD_REG(0x015A);
Write_LCD_REG(0x0187);
Write_LCD_REG(0x00C1);
Write_LCD_REG(0x013F);
Write_LCD_REG(0x00CD);
Write_LCD_REG(0x0125);
Write_LCD_REG(0x00C9);
Write_LCD_REG(0x0110);
Write_LCD_REG(0x00F8);
Write_LCD_REG(0x018A);
Write_LCD_REG(0x00AC);
Write_LCD_REG(0x0145);
Write_LCD_REG(0x00A7);
Write_LCD_REG(0x0147);
Write_LCD_REG(0x00A0);
Write_LCD_REG(0x01CC);
Write_LCD_REG(0x0086);
Write_LCD_REG(0x0199);
Write_LCD_REG(0x01A3);
Write_LCD_REG(0x01A3);
Write_LCD_REG(0x0131);
Write_LCD_REG(0x00FA);
Write_LCD_REG(0x0108);
Write_LCD_REG(0x0108);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x0104);
Write_LCD_REG(0x00A3);
Write_LCD_REG(0x016E);
Write_LCD_REG(0x00FD);
Write_LCD_REG(0x0128);
Write_LCD_REG(0x013C);
Write_LCD_REG(0x0100);

Write_LCD_REG(0x009A);
Write_LCD_REG(0x015a);
Write_LCD_REG(0x009B);
Write_LCD_REG(0x0122);
Write_LCD_REG(0x0082);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x0100);

Write_LCD_REG(0x0080);
Write_LCD_REG(0x0154);

Write_LCD_REG(0x00B1);
Write_LCD_REG(0x0133);
Write_LCD_REG(0x007A);
Write_LCD_REG(0x010F);
Write_LCD_REG(0x0113);
Write_LCD_REG(0x007B);
Write_LCD_REG(0x010F);
Write_LCD_REG(0x0113);
Write_LCD_REG(0x006D);

Write_LCD_REG(0x010C);

Write_LCD_REG(0x0103);
Write_LCD_REG(0x011e);
Write_LCD_REG(0x0102);
Write_LCD_REG(0x0108);
Write_LCD_REG(0x011a);
Write_LCD_REG(0x0119);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x010D);
Write_LCD_REG(0x010E);
Write_LCD_REG(0x010F);
Write_LCD_REG(0x0110);
Write_LCD_REG(0x011E);
Write_LCD_REG(0x011E);
Write_LCD_REG(0x011E);
Write_LCD_REG(0x011E);
Write_LCD_REG(0x011E);
Write_LCD_REG(0x011E);
Write_LCD_REG(0x011E);
Write_LCD_REG(0x011E);
Write_LCD_REG(0x0111);
Write_LCD_REG(0x0112);
Write_LCD_REG(0x0113);
Write_LCD_REG(0x0114);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x0119);
Write_LCD_REG(0x011a);
Write_LCD_REG(0x0107);
Write_LCD_REG(0x0101);
Write_LCD_REG(0x011E);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x010C);
Write_LCD_REG(0x0064);
Write_LCD_REG(0x0138);
Write_LCD_REG(0x0104);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x01c4);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x0138);
Write_LCD_REG(0x0102);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x01c6);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x012C);
Write_LCD_REG(0x017A);
Write_LCD_REG(0x012C);
Write_LCD_REG(0x017A);
Write_LCD_REG(0x0065);
Write_LCD_REG(0x0138);
Write_LCD_REG(0x0108);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x01c0);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x0138);
Write_LCD_REG(0x0106);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x01c2);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x012C);
Write_LCD_REG(0x017A);
Write_LCD_REG(0x012C);
Write_LCD_REG(0x017A);
Write_LCD_REG(0x0066);
Write_LCD_REG(0x0183);
Write_LCD_REG(0x01d0);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x01c4);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x0183);
Write_LCD_REG(0x01d0);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x01c4);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x012C);
Write_LCD_REG(0x017A);
Write_LCD_REG(0x012C);
Write_LCD_REG(0x017A);
Write_LCD_REG(0x0060);
Write_LCD_REG(0x0138);
Write_LCD_REG(0x010c);
Write_LCD_REG(0x013c);
Write_LCD_REG(0x013c);
Write_LCD_REG(0x0138);
Write_LCD_REG(0x010b);
Write_LCD_REG(0x013c);
Write_LCD_REG(0x013c);
Write_LCD_REG(0x0061);
Write_LCD_REG(0x01b3);
Write_LCD_REG(0x01c4);
Write_LCD_REG(0x013c);
Write_LCD_REG(0x013c);
Write_LCD_REG(0x01b3);
Write_LCD_REG(0x01c4);
Write_LCD_REG(0x013c);
Write_LCD_REG(0x013c);
Write_LCD_REG(0x0062);
Write_LCD_REG(0x01b3);
Write_LCD_REG(0x01c4);
Write_LCD_REG(0x013c);
Write_LCD_REG(0x013c);
Write_LCD_REG(0x01b3);
Write_LCD_REG(0x01c4);
Write_LCD_REG(0x013c);
Write_LCD_REG(0x013c);
Write_LCD_REG(0x0063);
Write_LCD_REG(0x0138);
Write_LCD_REG(0x010a);
Write_LCD_REG(0x013c);
Write_LCD_REG(0x013c);
Write_LCD_REG(0x0138);
Write_LCD_REG(0x0109);
Write_LCD_REG(0x013c);
Write_LCD_REG(0x013c);
Write_LCD_REG(0x0068);
Write_LCD_REG(0x0177);
Write_LCD_REG(0x0108);
Write_LCD_REG(0x010a);
Write_LCD_REG(0x0108);
Write_LCD_REG(0x0109);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x0118);
Write_LCD_REG(0x010a);
Write_LCD_REG(0x0108);
Write_LCD_REG(0x0109);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x0069);
Write_LCD_REG(0x0114);
Write_LCD_REG(0x0122);
Write_LCD_REG(0x0114);
Write_LCD_REG(0x0122);
Write_LCD_REG(0x0144);
Write_LCD_REG(0x0122);
Write_LCD_REG(0x0108);
Write_LCD_REG(0x006B);
Write_LCD_REG(0x0107);
Write_LCD_REG(0x00D1);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x0110);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x0122);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x012c);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x012e);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x0156);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x0158);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x017c);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x019a);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x01ce);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x01fa);
Write_LCD_REG(0x0101);
Write_LCD_REG(0x014c);
Write_LCD_REG(0x0101);
Write_LCD_REG(0x0194);
Write_LCD_REG(0x0101);
Write_LCD_REG(0x0196);
Write_LCD_REG(0x0101);
Write_LCD_REG(0x01da);
Write_LCD_REG(0x0102);
Write_LCD_REG(0x0132);
Write_LCD_REG(0x0102);
Write_LCD_REG(0x0176);
Write_LCD_REG(0x0102);
Write_LCD_REG(0x01cc);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x0118);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x0155);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x016b);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x019b);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x01ac);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x01b8);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x01e0);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x01FF);
Write_LCD_REG(0x00D2);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x0110);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x0122);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x012c);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x012e);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x0156);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x0158);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x017c);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x019a);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x01ce);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x01fa);
Write_LCD_REG(0x0101);
Write_LCD_REG(0x014c);
Write_LCD_REG(0x0101);
Write_LCD_REG(0x0194);
Write_LCD_REG(0x0101);
Write_LCD_REG(0x0196);
Write_LCD_REG(0x0101);
Write_LCD_REG(0x01da);
Write_LCD_REG(0x0102);
Write_LCD_REG(0x0132);
Write_LCD_REG(0x0102);
Write_LCD_REG(0x0176);
Write_LCD_REG(0x0102);
Write_LCD_REG(0x01cc);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x0118);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x0155);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x016b);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x019b);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x01ac);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x01b8);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x01e0);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x01FF);
Write_LCD_REG(0x00D3);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x0110);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x0122);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x012c);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x012e);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x0156);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x0158);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x017c);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x019a);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x01ce);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x01fa);
Write_LCD_REG(0x0101);
Write_LCD_REG(0x014c);
Write_LCD_REG(0x0101);
Write_LCD_REG(0x0194);
Write_LCD_REG(0x0101);
Write_LCD_REG(0x0196);
Write_LCD_REG(0x0101);
Write_LCD_REG(0x01da);
Write_LCD_REG(0x0102);
Write_LCD_REG(0x0132);
Write_LCD_REG(0x0102);
Write_LCD_REG(0x0176);
Write_LCD_REG(0x0102);
Write_LCD_REG(0x01cc);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x0118);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x0155);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x016b);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x019b);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x01ac);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x01b8);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x01e0);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x01FF);
Write_LCD_REG(0x00D4);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x0110);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x0122);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x012c);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x012e);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x0156);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x0158);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x017c);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x019a);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x01ce);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x01fa);
Write_LCD_REG(0x0101);
Write_LCD_REG(0x014c);
Write_LCD_REG(0x0101);
Write_LCD_REG(0x0194);
Write_LCD_REG(0x0101);
Write_LCD_REG(0x0196);
Write_LCD_REG(0x0101);
Write_LCD_REG(0x01da);
Write_LCD_REG(0x0102);
Write_LCD_REG(0x0132);
Write_LCD_REG(0x0102);
Write_LCD_REG(0x0176);
Write_LCD_REG(0x0102);
Write_LCD_REG(0x01cc);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x0118);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x0155);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x016b);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x019b);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x01ac);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x01b8);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x01e0);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x01FF);
Write_LCD_REG(0x00D5);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x0110);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x0122);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x012c);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x012e);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x0156);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x0158);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x017c);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x019a);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x01ce);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x01fa);
Write_LCD_REG(0x0101);
Write_LCD_REG(0x014c);
Write_LCD_REG(0x0101);
Write_LCD_REG(0x0194);
Write_LCD_REG(0x0101);
Write_LCD_REG(0x0196);
Write_LCD_REG(0x0101);
Write_LCD_REG(0x01da);
Write_LCD_REG(0x0102);
Write_LCD_REG(0x0132);
Write_LCD_REG(0x0102);
Write_LCD_REG(0x0176);
Write_LCD_REG(0x0102);
Write_LCD_REG(0x01cc);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x0118);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x0155);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x016b);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x019b);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x01ac);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x01b8);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x01e0);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x01FF);
Write_LCD_REG(0x00D6);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x0110);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x0122);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x012c);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x012e);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x0156);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x0158);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x017c);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x019a);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x01ce);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x01fa);
Write_LCD_REG(0x0101);
Write_LCD_REG(0x014c);
Write_LCD_REG(0x0101);
Write_LCD_REG(0x0194);
Write_LCD_REG(0x0101);
Write_LCD_REG(0x0196);
Write_LCD_REG(0x0101);
Write_LCD_REG(0x01da);
Write_LCD_REG(0x0102);
Write_LCD_REG(0x0132);
Write_LCD_REG(0x0102);
Write_LCD_REG(0x0176);
Write_LCD_REG(0x0102);
Write_LCD_REG(0x01cc);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x0118);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x0155);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x016b);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x019b);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x01ac);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x01b8);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x01e0);
Write_LCD_REG(0x0103);
Write_LCD_REG(0x01FF);


Write_LCD_REG(0x0011);
delay_us(200000);

Write_LCD_REG(0x0035);
Write_LCD_REG(0x0100);
Write_LCD_REG(0x0036);
Write_LCD_REG(0x0108);
Write_LCD_REG(0x003a);
Write_LCD_REG(0x0177);

Write_LCD_REG(0x0029);
delay_us(20000);
Write_LCD_REG(0x0011);	
delay_us(20000);
#endif
    return 0;
}

INIT_APP_EXPORT(GC9503V_Init);