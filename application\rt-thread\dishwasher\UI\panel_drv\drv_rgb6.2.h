#ifndef __DRV_RGB6_2_H__
#define __DRV_RGB6_2_H__
//-----------------LCD端口移植----------------
#define LCD_IOVCC rt_pin_get("PA.8")
#define LCD_DI rt_pin_get("PA.11")
#define LCD_SCL rt_pin_get("PA.10")
#define LCD_CS rt_pin_get("PA.9")

//-----------------LCD端口定义----------------

#define LCD_IOVCC_Clr() rt_pin_write(LCD_IOVCC, 0) // RES
#define LCD_IOVCC_Set() rt_pin_write(LCD_IOVCC, 1)

#define LCD_DI_Clr() rt_pin_write(LCD_DI, 0) // DC
#define LCD_DI_Set() rt_pin_write(LCD_DI, 1)

#define LCD_SCL_Clr() rt_pin_write(LCD_SCL, 0) // BLK
#define LCD_SCL_Set() rt_pin_write(LCD_SCL, 1)

#define LCD_CS_Clr() rt_pin_write(LCD_CS, 0) // CS
#define LCD_CS_Set() rt_pin_write(LCD_CS, 1)

#endif