# RT-Thread building script for component

from building import *

Import('rtconfig')

cwd     = GetCurrentDir()
src     = Glob('run.c')
src     += Glob('aicupg.c')
src     += Glob('aicupg_detect.c')
src     += Glob('aicupg_erase.c')
src     += Glob('ram_boot.c')
src     += Glob('crc32.c')
if GetDepend('AIC_WDT_DRV') and GetDepend('AIC_WRI_DRV'):
    src     += Glob('reset.c')
if GetDepend('AIC_BOOTLOADER_CMD_PROGRESS_BAR'):
    src     += Glob('progress_bar.c')
if GetDepend('AIC_BOOTLOADER_CMD_MEM'):
	src     += Glob('mem.c')
if GetDepend('AIC_BOOTLOADER_CMD_NOR_BOOT') and GetDepend('LPKG_USING_FDTLIB'):
	src     += Glob('nor_boot.c')
if GetDepend('AIC_BOOTLOADER_CMD_NOR_BOOT') and not GetDepend('LPKG_USING_FDTLIB'):
	src     += Glob('nor_boot_no_fitimage.c')
if GetDepend('AIC_BOOTLOADER_CMD_NAND_BOOT'):
	src     += Glob('nand_boot.c')
if GetDepend('AIC_BOOTLOADER_CMD_MMC_BOOT') and GetDepend('LPKG_USING_FDTLIB'):
	src     += Glob('mmc_boot.c')
if GetDepend('AIC_BOOTLOADER_CMD_MMC_BOOT') and not GetDepend('LPKG_USING_FDTLIB'):
	src     += Glob('mmc_boot_no_fitimage.c')
if GetDepend('AIC_BOOTLOADER_CMD_PSRAM_TEST'):
	src     += Glob('psram_test.c')
if GetDepend('AIC_BOOTLOADER_CMD_XIP_BOOT'):
	src     += Glob('xip_boot.c')
if GetDepend('AIC_BOOTLOADER_CMD_PART'):
	src     += Glob('part.c')

CPPPATH = [cwd]
ASFLAGS = ''

group = DefineGroup('BLCMD', src, depend = [''], CPPPATH = CPPPATH, ASFLAGS = ASFLAGS)

Return('group')
