Import('RTT_ROOT')
from building import *

cwd = GetCurrentDir()
src = Glob('*.c')

CPPPATH = [
    cwd,
    os.path.join(RTT_ROOT, 'include'),
    os.path.join(RTT_ROOT, 'components/drivers/include'),
]

CFLAGS = ' -c -ffunction-sections'

group = DefineGroup('use_lvgl', src, depend = [''], CPPPATH = CPPPATH, CFLAGS=CFLAGS)

# 根据不同的面板配置加载对应的LCD驱动
if GetDepend(['PANEL_6_2']):
    group = group + SConscript(os.path.join('lcd_6.2', 'SConscript'))
elif <PERSON>(['PANEL_4_3']):
    group = group + SConscript(os.path.join('lcd_4.3', 'SConscript'))

Return('group')
