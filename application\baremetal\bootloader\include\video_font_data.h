/*
 * Copyright (c) 2024, Artinchip Technology Co., Ltd
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 */

#ifndef _VIDEO_FONT_DATA_
#define _VIDEO_FONT_DATA_

#define VIDEO_FONT_CHARS    96
#define VIDEO_FONT_WIDTH    32
#define VIDEO_FONT_HEIGHT   32
#define VIDEO_FONT_SIZE     (VIDEO_FONT_CHARS * VIDEO_FONT_HEIGHT)

#ifdef AIC_BOOTLOADER_CMD_FB_CONSOLE
/**
 * ASCII 32 - 127
 */
static uint32_t video_fontdata[VIDEO_FONT_SIZE] = {
#if (AIC_BOOTLOADER_CMD_PROGRESS_BAR_ROTATE == 90)
    /* 0x20 ' ' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x21 '!' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x3ffc00f0,
    0x3ffffcf0,
    0x3ffffcf0,
    0x3ffffcf0,
    0x3ffc00f0,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x22 '"' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x3fe00000,
    0x3fe00000,
    0x3fe00000,
    0x00000000,
    0x00000000,
    0x3fe00000,
    0x3fe00000,
    0x3fe00000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x23 '#' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00180000,
    0x00183800,
    0x00183800,
    0x1fd83800,
    0x1fffb800,
    0x1ffffe00,
    0x03fffff0,
    0x001ffff0,
    0x00183ff0,
    0x10183800,
    0x1fd83800,
    0x1fffb800,
    0x1fffff80,
    0x01fffff0,
    0x0019fff0,
    0x00183bf0,
    0x00183800,
    0x00183800,
    0x00003800,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x24 '$' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00001e00,
    0x01807f80,
    0x0780ffc0,
    0x0f81ffe0,
    0x0f81fff0,
    0x1e01e070,
    0x1c03c070,
    0x1c03c030,
    0xffffffff,
    0xffffffff,
    0xffffffff,
    0x38070030,
    0x1c0f0070,
    0x1e0f0070,
    0x1ffe01f0,
    0x0ffe0fe0,
    0x07fc0fe0,
    0x03f80fc0,
    0x00000f00,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x25 '%' */
    0x00000000,
    0x00000fc0,
    0x00001ff0,
    0x00003ff0,
    0x00007878,
    0x00007038,
    0x00007018,
    0x08007018,
    0x0c007038,
    0x0f007878,
    0x07c03ff0,
    0x01f03ff0,
    0x00fc1fe0,
    0x003f0380,
    0x000f8000,
    0x0003e000,
    0x0000f800,
    0x00607e00,
    0x01fc1f80,
    0x03fe07c0,
    0x07ff01f0,
    0x0f0f0078,
    0x0e070038,
    0x0e030008,
    0x0e030000,
    0x0e070000,
    0x0f0f0000,
    0x07ff0000,
    0x07fe0000,
    0x01fc0000,
    0x00600000,
    0x00000000,

    /* 0x26 '&' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000010,
    0x00000030,
    0x00008070,
    0x0000f0f0,
    0x0000fff0,
    0x0000ffe0,
    0x0180ffc0,
    0x0fe01fc0,
    0x1ff07fe0,
    0x1ff8fce0,
    0x3ffdf870,
    0x3c3ff070,
    0x381fe030,
    0x381fc030,
    0x3c3f0030,
    0x1fff8070,
    0x1fffc070,
    0x0ffbe0f0,
    0x07e1fff0,
    0x0001ffe0,
    0x0000ffc0,
    0x00003f80,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x27 ''' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x3fe00000,
    0x3fe00000,
    0x3fe00000,
    0x3fe00000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x28 '(' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x30000000,
    0x3c000001,
    0x3f000007,
    0x1fe0003f,
    0x07ffffff,
    0x03fffffc,
    0x00fffff0,
    0x001fffc0,
    0x0000fc00,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x29 ')' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x0000f000,
    0x000fff80,
    0x007ffff0,
    0x01fffffc,
    0x07ffffff,
    0x0ff0007f,
    0x3f80000f,
    0x3c000001,
    0x30000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x2a '*' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x06000000,
    0x06300000,
    0x07700000,
    0x03f00000,
    0x3fe00000,
    0x3fc00000,
    0x3fe00000,
    0x03f00000,
    0x07700000,
    0x06300000,
    0x06000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x2b '+' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00003000,
    0x00003000,
    0x00003000,
    0x00003000,
    0x00003000,
    0x00003000,
    0x00003000,
    0x00003000,
    0x003ffff0,
    0x003ffff0,
    0x003ffff0,
    0x003ffff0,
    0x00003000,
    0x00003000,
    0x00003000,
    0x00003000,
    0x00003000,
    0x00003000,
    0x00003000,
    0x00003000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x2c ',' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x000000fc,
    0x000000ff,
    0x000000ff,
    0x000000f3,
    0x000000f1,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x2d '-' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00003000,
    0x00003000,
    0x00003000,
    0x00003000,
    0x00003000,
    0x00003000,
    0x00003000,
    0x00003000,
    0x00003000,
    0x00003000,
    0x00003000,
    0x00003000,
    0x00003000,
    0x00003000,
    0x00003000,
    0x00003000,
    0x00003000,
    0x00003000,
    0x00003000,
    0x00003000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x2e '.' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x000000f0,
    0x000000f0,
    0x000000f0,
    0x000000f0,
    0x000000f0,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x2f '/' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x38000000,
    0x3f000000,
    0x3fe00000,
    0x0ffc0000,
    0x01ff8000,
    0x003ff000,
    0x0007fe00,
    0x0000ffc0,
    0x00000ff0,
    0x000001f0,
    0x00000030,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x30 '0' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x0007e000,
    0x007ffe00,
    0x03ffff80,
    0x07ffffc0,
    0x0fffffe0,
    0x0fc003f0,
    0x1f0000f0,
    0x1c000070,
    0x1c000030,
    0x1c000030,
    0x1c000030,
    0x1c000070,
    0x1e0000f0,
    0x1f8003f0,
    0x0fffffe0,
    0x07ffffc0,
    0x03ffff80,
    0x00fffe00,
    0x000fe000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x31 '1' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x1ffffff0,
    0x1ffffff0,
    0x1ffffff0,
    0x1ffffff0,
    0x07c00000,
    0x03c00000,
    0x01c00000,
    0x01c00000,
    0x00c00000,
    0x00c00000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x32 '2' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x01fc0070,
    0x07fe0070,
    0x0fff0070,
    0x0fff8070,
    0x1f0fc070,
    0x1e03c070,
    0x1c01e070,
    0x1c01e070,
    0x1c00f070,
    0x1c00f070,
    0x1c007870,
    0x1e003c70,
    0x1f003e70,
    0x0ff01ff0,
    0x0ff00ff0,
    0x07f007f0,
    0x01f003f0,
    0x000000f0,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x33 '3' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00003e00,
    0x00e07f80,
    0x07f8ffc0,
    0x0ffdffe0,
    0x0fffffe0,
    0x1fffc0f0,
    0x1e0f8070,
    0x1c078070,
    0x1c070030,
    0x1c030030,
    0x1c030030,
    0x1c030030,
    0x1e000070,
    0x1f0000f0,
    0x0fe00ff0,
    0x0fe00fe0,
    0x07e00fe0,
    0x01e00f80,
    0x00000e00,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x34 '4' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00001c00,
    0x00001c00,
    0x00001c00,
    0x00001c00,
    0x1ffffff0,
    0x1ffffff0,
    0x1ffffff0,
    0x1ffffff0,
    0x0f801c00,
    0x07e01c00,
    0x01f01c00,
    0x00fc1c00,
    0x007e1c00,
    0x001f9c00,
    0x000fdc00,
    0x0003fc00,
    0x0001fc00,
    0x0000fc00,
    0x00003c00,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x35 '5' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00001000,
    0x0000ff00,
    0x1c03ffc0,
    0x1c03ffc0,
    0x1c07ffe0,
    0x1c0fc1f0,
    0x1c0f00f0,
    0x1c0e0070,
    0x1c0e0030,
    0x1c0e0030,
    0x1c0e0030,
    0x1c0e0030,
    0x1c0f0070,
    0x1ff700f0,
    0x1fff83f0,
    0x1fff83e0,
    0x01ff83e0,
    0x000383c0,
    0x00000300,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x36 '6' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00003c00,
    0x0180ff80,
    0x0783ffc0,
    0x0f87ffe0,
    0x0f87fff0,
    0x1f0780f0,
    0x1e0f0070,
    0x1c0e0070,
    0x1c0e0030,
    0x1c0e0030,
    0x1c0e0030,
    0x1e0e0070,
    0x1f0700f0,
    0x0fc7c1f0,
    0x0fffffe0,
    0x07ffffc0,
    0x01ffff80,
    0x007fff00,
    0x0003e000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x37 '7' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x1c000000,
    0x1f000000,
    0x1f800000,
    0x1fc00000,
    0x1ff00000,
    0x1dfc0000,
    0x1c7e0000,
    0x1c3f8000,
    0x1c1ff000,
    0x1c07fe00,
    0x1c01ffe0,
    0x1c007ff0,
    0x1c001ff0,
    0x1c0003f0,
    0x1c000070,
    0x1c000000,
    0x1c000000,
    0x1c000000,
    0x1c000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x38 '8' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000800,
    0x00407f80,
    0x03f8ffc0,
    0x07fdffe0,
    0x0fffffe0,
    0x1fffc0f0,
    0x1e0f8070,
    0x1c078030,
    0x1c070030,
    0x1c070030,
    0x1c070030,
    0x1c078070,
    0x1e0f8070,
    0x1fbfc0f0,
    0x0ffffff0,
    0x0ffdffe0,
    0x03f8ffc0,
    0x00e07f80,
    0x00001e00,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x39 '9' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00060000,
    0x00fff800,
    0x03ffff00,
    0x07ffff80,
    0x0fffffc0,
    0x1f87c7e0,
    0x1e01e1f0,
    0x1c00e070,
    0x1c006030,
    0x1c007030,
    0x1c007030,
    0x1c00e030,
    0x1e00e070,
    0x1f01e0f0,
    0x0fffe3f0,
    0x0fffc3e0,
    0x07ff83c0,
    0x01ff0380,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x3a ':' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x007800f0,
    0x007800f0,
    0x007800f0,
    0x007800f0,
    0x007800f0,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x3b ';' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x007800fc,
    0x007800ff,
    0x007800ff,
    0x007800f3,
    0x007800f1,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x3c '<' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x001800e0,
    0x001c00e0,
    0x001c00e0,
    0x001e01c0,
    0x000e01c0,
    0x000e03c0,
    0x00070380,
    0x00070380,
    0x00038700,
    0x00038700,
    0x0003cf00,
    0x0001ce00,
    0x0001de00,
    0x0000fc00,
    0x0000fc00,
    0x0000f800,
    0x00007800,
    0x00007800,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x3d '=' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00038700,
    0x00038700,
    0x00038700,
    0x00038700,
    0x00038700,
    0x00038700,
    0x00038700,
    0x00038700,
    0x00038700,
    0x00038700,
    0x00038700,
    0x00038700,
    0x00038700,
    0x00038700,
    0x00038700,
    0x00038700,
    0x00038700,
    0x00038700,
    0x00038700,
    0x00038700,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x3e '>' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00007800,
    0x00007800,
    0x0000f800,
    0x0000fc00,
    0x0000fc00,
    0x0001de00,
    0x0001ce00,
    0x0003ce00,
    0x00038700,
    0x00038700,
    0x00070780,
    0x00070380,
    0x000e03c0,
    0x000e01c0,
    0x001e01c0,
    0x001c00e0,
    0x001c00e0,
    0x001800e0,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x3f '?' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00c00000,
    0x07f00000,
    0x0ffc0000,
    0x1ffe0000,
    0x1fff0000,
    0x3c1f8000,
    0x3807c000,
    0x3803f8f0,
    0x3801f8f0,
    0x3800f8f0,
    0x380078f0,
    0x3c000000,
    0x1f000000,
    0x1fc00000,
    0x0fc00000,
    0x07c00000,
    0x01c00000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x40 '@' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x003f8000,
    0x00fff100,
    0x01fff9c0,
    0x03f0fdc0,
    0x07801ee0,
    0x0f600e70,
    0x0e7e0770,
    0x1c7fe338,
    0x1c3fff18,
    0x187fff18,
    0x1878ff18,
    0x18f01e18,
    0x18e00e18,
    0x18600718,
    0x18700718,
    0x18780718,
    0x1c7e0738,
    0x1c3fff38,
    0x0e1fff70,
    0x0e0ffe70,
    0x0783fce0,
    0x07c003e0,
    0x03f80fc0,
    0x01ffff80,
    0x007ffe00,
    0x001ff800,
    0x00018000,
    0x00000000,
    0x00000000,

    /* 0x41 'A' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000070,
    0x000003f0,
    0x00000ff0,
    0x00007ff0,
    0x0003ffe0,
    0x001fff80,
    0x007ffc00,
    0x03fff800,
    0x1fff7800,
    0x3ff87800,
    0x3fc07800,
    0x3e007800,
    0x3fc07800,
    0x3ff87800,
    0x0fff7800,
    0x01fff800,
    0x003ff800,
    0x000fff00,
    0x0001ffe0,
    0x00003ff0,
    0x00000ff0,
    0x000001f0,
    0x00000030,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x42 'B' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00001c00,
    0x00007f00,
    0x03f0ffc0,
    0x0ff9ffc0,
    0x1fffffe0,
    0x1fffc1e0,
    0x1f3f80f0,
    0x3c0f80f0,
    0x3c070070,
    0x38070070,
    0x38070070,
    0x38070070,
    0x38070070,
    0x38070070,
    0x38070070,
    0x38070070,
    0x38070070,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x43 'C' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00c03c00,
    0x03c03f00,
    0x07c03f80,
    0x0fc03fc0,
    0x1f8007e0,
    0x1e0001e0,
    0x3c0000f0,
    0x38000070,
    0x38000070,
    0x38000070,
    0x38000030,
    0x38000030,
    0x38000070,
    0x38000070,
    0x3c000070,
    0x3c0000f0,
    0x1e0001f0,
    0x1f8003e0,
    0x0ff01fe0,
    0x0fffffc0,
    0x03ffff80,
    0x01fffe00,
    0x007ff800,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x44 'D' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x003ff000,
    0x00fffc00,
    0x03ffff00,
    0x07ffff80,
    0x0ff87fc0,
    0x0f8007e0,
    0x1f0001e0,
    0x1e0000f0,
    0x1c0000f0,
    0x3c000070,
    0x38000070,
    0x38000070,
    0x38000070,
    0x38000070,
    0x38000070,
    0x38000070,
    0x38000070,
    0x38000070,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x45 'E' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x38000070,
    0x38070070,
    0x38070070,
    0x38070070,
    0x38070070,
    0x38070070,
    0x38070070,
    0x38070070,
    0x38070070,
    0x38070070,
    0x38070070,
    0x38070070,
    0x38070070,
    0x38070070,
    0x38070070,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x46 'F' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x38000000,
    0x38000000,
    0x38070000,
    0x38070000,
    0x38070000,
    0x38070000,
    0x38070000,
    0x38070000,
    0x38070000,
    0x38070000,
    0x38070000,
    0x38070000,
    0x38070000,
    0x38070000,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x47 'G' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00c3fff0,
    0x03c3fff0,
    0x07c3fff0,
    0x0fc3ffc0,
    0x1fc387c0,
    0x1f0381e0,
    0x3e0380e0,
    0x3c038070,
    0x38038070,
    0x38038070,
    0x38038030,
    0x38038030,
    0x38000030,
    0x38000070,
    0x38000070,
    0x3c000070,
    0x3e0000f0,
    0x1f0001f0,
    0x1f8007e0,
    0x0ff83fc0,
    0x07ffffc0,
    0x03ffff00,
    0x00fffe00,
    0x003ff800,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x48 'H' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x00070000,
    0x00070000,
    0x00070000,
    0x00070000,
    0x00070000,
    0x00070000,
    0x00070000,
    0x00070000,
    0x00070000,
    0x00070000,
    0x00070000,
    0x00070000,
    0x00070000,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x49 'I' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x4a 'J' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x3fffff80,
    0x3fffffc0,
    0x3fffffe0,
    0x3ffffff0,
    0x000001f0,
    0x00000070,
    0x00000030,
    0x00000030,
    0x00000030,
    0x00000070,
    0x00000070,
    0x00000ff0,
    0x00000fe0,
    0x00000fe0,
    0x00000fc0,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x4b 'K' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000010,
    0x20000030,
    0x30000070,
    0x380001f0,
    0x3c0003f0,
    0x3e0007f0,
    0x3f001fe0,
    0x1f803f80,
    0x0fc0ff00,
    0x07e1fe00,
    0x03f3f800,
    0x01fff000,
    0x00ffc000,
    0x007f8000,
    0x003f0000,
    0x001f8000,
    0x000fc000,
    0x0007e000,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x4c 'L' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000070,
    0x00000070,
    0x00000070,
    0x00000070,
    0x00000070,
    0x00000070,
    0x00000070,
    0x00000070,
    0x00000070,
    0x00000070,
    0x00000070,
    0x00000070,
    0x00000070,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x4d 'M' */
    0x00000000,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x3fe00000,
    0x3ffc0000,
    0x07ff8000,
    0x00fff000,
    0x001ffe00,
    0x0003ffc0,
    0x00007ff0,
    0x00000ff0,
    0x000003f0,
    0x00000ff0,
    0x00007ff0,
    0x0003ffc0,
    0x001ffe00,
    0x00fff000,
    0x07ff8000,
    0x3ffc0000,
    0x3fe00000,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x4e 'N' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x000007f0,
    0x00001fe0,
    0x00003f80,
    0x0000ff00,
    0x0001fc00,
    0x0007f000,
    0x001fe000,
    0x003f8000,
    0x00ff0000,
    0x03fc0000,
    0x07f00000,
    0x1fe00000,
    0x3f800000,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x4f 'O' */
    0x00000000,
    0x00030000,
    0x007ff800,
    0x01fffe00,
    0x03ffff00,
    0x07ffff80,
    0x0fe01fc0,
    0x1f8007e0,
    0x1e0001e0,
    0x3c0000f0,
    0x3c0000f0,
    0x38000070,
    0x38000070,
    0x38000070,
    0x38000030,
    0x38000070,
    0x38000070,
    0x38000070,
    0x3c0000f0,
    0x3c0000f0,
    0x1e0001e0,
    0x1f8007e0,
    0x0ff01fc0,
    0x07ffff80,
    0x03ffff00,
    0x01fffe00,
    0x007ff800,
    0x00030000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x50 'P' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x03f80000,
    0x07fe0000,
    0x0fff0000,
    0x1fff0000,
    0x1f0f8000,
    0x3c078000,
    0x3c038000,
    0x38038000,
    0x38038000,
    0x38038000,
    0x38038000,
    0x38038000,
    0x38038000,
    0x38038000,
    0x38038000,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x51 'Q' */
    0x00000000,
    0x00030000,
    0x007ff818,
    0x01fffe3c,
    0x03ffff3c,
    0x07fffff8,
    0x0fe01ff0,
    0x1f8007f0,
    0x1e0001e0,
    0x3c0003f0,
    0x3c0007f0,
    0x380007f0,
    0x38000770,
    0x38000230,
    0x38000030,
    0x38000070,
    0x38000070,
    0x38000070,
    0x3c0000f0,
    0x3c0000f0,
    0x1e0001e0,
    0x1f8007e0,
    0x0ff01fc0,
    0x07ffff80,
    0x03ffff00,
    0x01fffe00,
    0x007ff800,
    0x00030000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x52 'R' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000010,
    0x00e00010,
    0x07f83ff0,
    0x0ffcfff0,
    0x1ffefff0,
    0x1ffffff0,
    0x1e0ff000,
    0x3c07c000,
    0x38038000,
    0x38038000,
    0x38038000,
    0x38038000,
    0x38038000,
    0x38038000,
    0x38038000,
    0x38038000,
    0x38038000,
    0x38038000,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x53 'S' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00403f00,
    0x03c0ffc0,
    0x0fc1ffc0,
    0x1fc1ffe0,
    0x1fc3e1f0,
    0x3e03c0f0,
    0x3c03c070,
    0x38078070,
    0x38078030,
    0x38078030,
    0x38078030,
    0x38070030,
    0x380f0070,
    0x380f0070,
    0x3c0f0070,
    0x3e1e00f0,
    0x1ffe03f0,
    0x1ffe0fe0,
    0x0ffc0fc0,
    0x03f80f80,
    0x00000f00,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x54 'T' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x38000000,
    0x38000000,
    0x38000000,
    0x38000000,
    0x38000000,
    0x38000000,
    0x38000000,
    0x38000000,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x38000000,
    0x38000000,
    0x38000000,
    0x38000000,
    0x38000000,
    0x38000000,
    0x38000000,
    0x38000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x55 'U' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x3ffffe00,
    0x3fffff80,
    0x3fffffc0,
    0x3fffffe0,
    0x00000fe0,
    0x000001f0,
    0x000000f0,
    0x00000070,
    0x00000070,
    0x00000070,
    0x00000030,
    0x00000030,
    0x00000070,
    0x00000070,
    0x000000f0,
    0x000001f0,
    0x000007e0,
    0x3fffffe0,
    0x3fffffc0,
    0x3fffff80,
    0x3ffffe00,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x56 'V' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x38000000,
    0x3e000000,
    0x3fc00000,
    0x3ff80000,
    0x3ffe0000,
    0x07ffc000,
    0x00fff800,
    0x001fff00,
    0x0003ffc0,
    0x00007ff0,
    0x00000ff0,
    0x000001f0,
    0x00000ff0,
    0x00007ff0,
    0x0003ffc0,
    0x001fff00,
    0x00fff800,
    0x07ffc000,
    0x3ffe0000,
    0x3ff80000,
    0x3fc00000,
    0x3e000000,
    0x30000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x57 'W' */
    0x01ffff00,
    0x001ffff0,
    0x0000fff0,
    0x00000ff0,
    0x00001ff0,
    0x0000fff0,
    0x000fffc0,
    0x00fffe00,
    0x07ffe000,
    0x3fff0000,
    0x3ff00000,
    0x3f000000,
    0x3ff00000,
    0x3ffe0000,
    0x0fffe000,
    0x00fffc00,
    0x000fffc0,
    0x0001fff0,
    0x00001ff0,
    0x00000ff0,
    0x0000fff0,
    0x000ffff0,
    0x00ffff80,
    0x1ffff800,
    0x3fff8000,
    0x3ff80000,
    0x3fc00000,
    0x3c000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x58 'X' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x20000010,
    0x30000070,
    0x3c0000f0,
    0x3e0003f0,
    0x3f8007f0,
    0x1fc00fe0,
    0x0fe03fc0,
    0x03f87f00,
    0x01fdfe00,
    0x007ff800,
    0x003ff000,
    0x001fc000,
    0x003ff000,
    0x00fff800,
    0x01fdfe00,
    0x07f87f00,
    0x0fe03fc0,
    0x3fc00fe0,
    0x3f0007f0,
    0x3e0001f0,
    0x380000f0,
    0x30000070,
    0x20000010,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x59 'Y' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x30000000,
    0x38000000,
    0x3e000000,
    0x3f800000,
    0x3fc00000,
    0x0ff00000,
    0x07f80000,
    0x01fe0000,
    0x007f0000,
    0x003ffff0,
    0x000ffff0,
    0x0003fff0,
    0x000ffff0,
    0x003ffff0,
    0x007f0000,
    0x01fe0000,
    0x07f80000,
    0x0ff00000,
    0x3fc00000,
    0x3f000000,
    0x3e000000,
    0x38000000,
    0x30000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x5a 'Z' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x3c000070,
    0x3e000070,
    0x3f000070,
    0x3fc00070,
    0x3fe00070,
    0x3ff00070,
    0x39f80070,
    0x38fe0070,
    0x387f0070,
    0x383f8070,
    0x380fc070,
    0x3807f070,
    0x3803f870,
    0x3801fc70,
    0x38007e70,
    0x38003ff0,
    0x38001ff0,
    0x380007f0,
    0x380003f0,
    0x380001f0,
    0x000000f0,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x5b '[' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x38000000,
    0x38000000,
    0x38000000,
    0x38000000,
    0x3fffffff,
    0x3fffffff,
    0x3fffffff,
    0x3fffffff,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x5c '\' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000070,
    0x000003f0,
    0x00001ff0,
    0x0000ffc0,
    0x0007fe00,
    0x003ff000,
    0x01ff8000,
    0x0ffc0000,
    0x3fc00000,
    0x3e000000,
    0x30000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x5d ']' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x3fffffff,
    0x3fffffff,
    0x3fffffff,
    0x3fffffff,
    0x38000000,
    0x38000000,
    0x38000000,
    0x38000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x5e '^' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x02000000,
    0x0e000000,
    0x1e000000,
    0x3e000000,
    0x3c000000,
    0x30000000,
    0x38000000,
    0x3e000000,
    0x1e000000,
    0x0e000000,
    0x02000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x5f '_' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000003,
    0x00000003,
    0x00000003,
    0x00000003,
    0x00000003,
    0x00000003,
    0x00000003,
    0x00000003,
    0x00000003,
    0x00000003,
    0x00000003,
    0x00000003,
    0x00000003,
    0x00000003,
    0x00000003,
    0x00000003,
    0x00000003,
    0x00000003,
    0x00000003,
    0x00000003,
    0x00000003,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x60 '`' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x06000000,
    0x1e000000,
    0x3e000000,
    0x3c000000,
    0x38000000,
    0x30000000,
    0x20000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x61 'a' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000030,
    0x00000030,
    0x000ffff0,
    0x001ffff0,
    0x003ffff0,
    0x007fffe0,
    0x0078e1e0,
    0x007060e0,
    0x00706070,
    0x00607070,
    0x00607030,
    0x00707030,
    0x00707030,
    0x007c7870,
    0x007e7ff0,
    0x003e3ff0,
    0x001e3fe0,
    0x00061fc0,
    0x00000780,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x62 'b' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00006000,
    0x0007fe00,
    0x001fff80,
    0x003fffc0,
    0x003fffe0,
    0x007c01f0,
    0x00780070,
    0x00700070,
    0x00700030,
    0x00700030,
    0x00700030,
    0x00700070,
    0x007800f0,
    0x003f03e0,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x63 'c' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00070700,
    0x001f07c0,
    0x003f07e0,
    0x007f07e0,
    0x007c01f0,
    0x00700070,
    0x00700070,
    0x00700030,
    0x00700030,
    0x00700030,
    0x00780070,
    0x007c00f0,
    0x003f87e0,
    0x003fffe0,
    0x001fffc0,
    0x0007ff80,
    0x0000fc00,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x64 'd' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x001f83e0,
    0x003c00f0,
    0x00780070,
    0x00700070,
    0x00700030,
    0x00700030,
    0x00700070,
    0x00780070,
    0x007c01f0,
    0x003fffe0,
    0x003fffe0,
    0x001fffc0,
    0x0007ff00,
    0x0000f800,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x65 'e' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00003000,
    0x0003f300,
    0x000ff3c0,
    0x001ff3e0,
    0x003ff3f0,
    0x007c70f0,
    0x00787070,
    0x00707070,
    0x00707030,
    0x00707030,
    0x00707030,
    0x00707070,
    0x00787070,
    0x007c71f0,
    0x003fffe0,
    0x001fffe0,
    0x000fffc0,
    0x0007ff00,
    0x00003000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x66 'f' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x38700000,
    0x38700000,
    0x38700000,
    0x3ffffff0,
    0x3ffffff0,
    0x1ffffff0,
    0x0ffffff0,
    0x00700000,
    0x00700000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x67 'g' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x007ffffc,
    0x007fffff,
    0x007fffff,
    0x007fffff,
    0x003f03e3,
    0x007800f0,
    0x00700070,
    0x00700030,
    0x00700030,
    0x00700030,
    0x00700070,
    0x007800f0,
    0x007c01f1,
    0x003fffe3,
    0x003fffc3,
    0x001fff83,
    0x0007ff03,
    0x00007000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x68 'h' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x0003fff0,
    0x001ffff0,
    0x003ffff0,
    0x007ffff0,
    0x007ffff0,
    0x00700000,
    0x00700000,
    0x00700000,
    0x00700000,
    0x00700000,
    0x00780000,
    0x003c0000,
    0x001f8000,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x69 'i' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x3c7ffff0,
    0x3c7ffff0,
    0x3c7ffff0,
    0x3c7ffff0,
    0x3c7ffff0,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x6a 'j' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x3c7fffff,
    0x3c7fffff,
    0x3c7fffff,
    0x3c7fffff,
    0x3c7fffff,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x6b 'k' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000010,
    0x00400030,
    0x006000f0,
    0x007001f0,
    0x007807f0,
    0x007c1fe0,
    0x003e3fc0,
    0x001fff00,
    0x000ffc00,
    0x0007f800,
    0x0003e000,
    0x0001f000,
    0x0000f800,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x6c 'l' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x6d 'm' */
    0x0007fff0,
    0x001ffff0,
    0x003ffff0,
    0x007ffff0,
    0x007ffff0,
    0x00780000,
    0x00700000,
    0x00700000,
    0x00700000,
    0x00700000,
    0x00780000,
    0x003ffff0,
    0x001ffff0,
    0x003ffff0,
    0x007ffff0,
    0x007ffff0,
    0x00700000,
    0x00700000,
    0x00700000,
    0x00700000,
    0x00780000,
    0x003c0000,
    0x003f8000,
    0x007ffff0,
    0x007ffff0,
    0x007ffff0,
    0x007ffff0,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x6e 'n' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x0003fff0,
    0x001ffff0,
    0x003ffff0,
    0x007ffff0,
    0x007ffff0,
    0x00780000,
    0x00700000,
    0x00700000,
    0x00700000,
    0x00700000,
    0x00780000,
    0x003c0000,
    0x003f8000,
    0x007ffff0,
    0x007ffff0,
    0x007ffff0,
    0x007ffff0,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x6f 'o' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00007000,
    0x0007ff00,
    0x000fff80,
    0x001fffc0,
    0x003fffe0,
    0x007c01f0,
    0x007800f0,
    0x00700070,
    0x00700030,
    0x00600030,
    0x00700030,
    0x00700070,
    0x007800f0,
    0x007c01f0,
    0x003fffe0,
    0x001fffc0,
    0x000fff80,
    0x0007ff00,
    0x0000f800,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x70 'p' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00006000,
    0x0007fe00,
    0x001fff80,
    0x003fffc0,
    0x003fffe0,
    0x007c01f0,
    0x00780070,
    0x00700070,
    0x00700030,
    0x00700030,
    0x00700030,
    0x00700070,
    0x007800f0,
    0x003f03e0,
    0x007fffff,
    0x007fffff,
    0x007fffff,
    0x007fffff,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x71 'q' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x007fffff,
    0x007fffff,
    0x007fffff,
    0x007fffff,
    0x001f83e0,
    0x003c00f0,
    0x00780070,
    0x00700070,
    0x00700030,
    0x00700030,
    0x00700070,
    0x00780070,
    0x007c01f0,
    0x003fffe0,
    0x003fffe0,
    0x001fffc0,
    0x0007ff00,
    0x0000f800,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x72 'r' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00700000,
    0x00700000,
    0x00700000,
    0x00780000,
    0x00780000,
    0x003e0000,
    0x007ffff0,
    0x007ffff0,
    0x007ffff0,
    0x007ffff0,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x73 's' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000600,
    0x000e1fc0,
    0x001e1fe0,
    0x003e3fe0,
    0x007e3ff0,
    0x00787870,
    0x00707870,
    0x00707030,
    0x00607030,
    0x0070f030,
    0x0070f070,
    0x0070e070,
    0x007fe0f0,
    0x003fe3e0,
    0x003fc3e0,
    0x000f83c0,
    0x00000300,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x74 't' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00700030,
    0x00700030,
    0x00700070,
    0x0ffffff0,
    0x0ffffff0,
    0x0ffffff0,
    0x0fffffe0,
    0x00700000,
    0x00700000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x75 'u' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x007ffff0,
    0x007ffff0,
    0x007ffff0,
    0x007ffff0,
    0x007fffc0,
    0x000001e0,
    0x00000070,
    0x00000070,
    0x00000030,
    0x00000030,
    0x00000030,
    0x00000070,
    0x007ffff0,
    0x007ffff0,
    0x007fffe0,
    0x007fffc0,
    0x007fff00,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x76 'v' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00700000,
    0x007e0000,
    0x007f8000,
    0x007ff000,
    0x001ffe00,
    0x0003ff80,
    0x00007ff0,
    0x00000ff0,
    0x000001f0,
    0x000007f0,
    0x00007ff0,
    0x0003ffc0,
    0x001ffe00,
    0x007ff800,
    0x007fc000,
    0x007e0000,
    0x00700000,
    0x00400000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x77 'w' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00700000,
    0x007f0000,
    0x007fe000,
    0x007ffe00,
    0x000fffc0,
    0x0000fff0,
    0x00000ff0,
    0x00000ff0,
    0x0000fff0,
    0x000fffe0,
    0x007ffe00,
    0x007fe000,
    0x007f0000,
    0x007fe000,
    0x007ffe00,
    0x000fffe0,
    0x0000fff0,
    0x00000ff0,
    0x00000ff0,
    0x0000fff0,
    0x000fffe0,
    0x007ffe00,
    0x007ff000,
    0x007f0000,
    0x00700000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x78 'x' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00400030,
    0x00700070,
    0x007801f0,
    0x007e03f0,
    0x007f07f0,
    0x001fdfc0,
    0x000fff80,
    0x0003fe00,
    0x0001fc00,
    0x0007fe00,
    0x000fff80,
    0x003fdfc0,
    0x007f07f0,
    0x007e03f0,
    0x007801f0,
    0x00700070,
    0x00400030,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x79 'y' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00700000,
    0x007e0000,
    0x007fc000,
    0x007ff000,
    0x001ffe00,
    0x0003ffc0,
    0x00007ff0,
    0x00000ffc,
    0x000001ff,
    0x00000fff,
    0x00007fff,
    0x0003ffc3,
    0x001ffe00,
    0x007ff000,
    0x007fc000,
    0x007e0000,
    0x00700000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x7a 'z' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000070,
    0x00780070,
    0x007c0070,
    0x007e0070,
    0x007f8070,
    0x007fc070,
    0x0077e070,
    0x0073f070,
    0x0071fc70,
    0x00707e70,
    0x00703f70,
    0x00701ff0,
    0x00700ff0,
    0x007007f0,
    0x007001f0,
    0x007000f0,
    0x00000070,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x7b '{' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x30000000,
    0x30000000,
    0x30000000,
    0x38000001,
    0x3fff07ff,
    0x1fffcfff,
    0x1fffdfff,
    0x07fffffe,
    0x0000f800,
    0x00007000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x7c '|' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x7d '}' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00007000,
    0x00007000,
    0x0000f800,
    0x03fffffe,
    0x1fffdfff,
    0x1fffdfff,
    0x3fff07ff,
    0x3c000001,
    0x30000000,
    0x30000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x7e '~' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00002000,
    0x0000f000,
    0x0000f800,
    0x00003800,
    0x00001800,
    0x00001800,
    0x00003800,
    0x00003800,
    0x00003800,
    0x00007000,
    0x00007000,
    0x00006000,
    0x0000e000,
    0x0000e000,
    0x00007000,
    0x00007800,
    0x00003800,
    0x00001000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x7f '' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x3ffffff8,
    0x3ffffff8,
    0x3ffffff8,
    0x30000018,
    0x30000018,
    0x30000018,
    0x30000018,
    0x3ffffff8,
    0x3ffffff8,
    0x3ffffff8,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
#elif (AIC_BOOTLOADER_CMD_PROGRESS_BAR_ROTATE == 270)
    /* 0x20 ' ' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x21 '!' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x3ffc00f0,
    0x3ffffcf0,
    0x3ffffcf0,
    0x3ffffcf0,
    0x3ffc00f0,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x22 '"' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x3fe00000,
    0x3fe00000,
    0x3fe00000,
    0x00000000,
    0x00000000,
    0x3fe00000,
    0x3fe00000,
    0x3fe00000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x23 '#' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00003800,
    0x00183800,
    0x00183800,
    0x00183bf0,
    0x0019fff0,
    0x01fffff0,
    0x1fffff80,
    0x1fffb800,
    0x1fd83800,
    0x10183800,
    0x00183ff0,
    0x001ffff0,
    0x03fffff0,
    0x1ffffe00,
    0x1fffb800,
    0x1fd83800,
    0x00183800,
    0x00183800,
    0x00180000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x24 '$' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000f00,
    0x03f80fc0,
    0x07fc0fe0,
    0x0ffe0fe0,
    0x1ffe01f0,
    0x1e0f0070,
    0x1c0f0070,
    0x38070030,
    0xffffffff,
    0xffffffff,
    0xffffffff,
    0x1c03c030,
    0x1c03c070,
    0x1e01e070,
    0x0f81fff0,
    0x0f81ffe0,
    0x0780ffc0,
    0x01807f80,
    0x00001e00,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x25 '%' */
    0x00000000,
    0x00600000,
    0x01fc0000,
    0x07fe0000,
    0x07ff0000,
    0x0f0f0000,
    0x0e070000,
    0x0e030000,
    0x0e030008,
    0x0e070038,
    0x0f0f0078,
    0x07ff01f0,
    0x03fe07c0,
    0x01fc1f80,
    0x00607e00,
    0x0000f800,
    0x0003e000,
    0x000f8000,
    0x003f0380,
    0x00fc1fe0,
    0x01f03ff0,
    0x07c03ff0,
    0x0f007878,
    0x0c007038,
    0x08007018,
    0x00007018,
    0x00007038,
    0x00007878,
    0x00003ff0,
    0x00001ff0,
    0x00000fc0,
    0x00000000,

    /* 0x26 '&' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00003f80,
    0x0000ffc0,
    0x0001ffe0,
    0x07e1fff0,
    0x0ffbe0f0,
    0x1fffc070,
    0x1fff8070,
    0x3c3f0030,
    0x381fc030,
    0x381fe030,
    0x3c3ff070,
    0x3ffdf870,
    0x1ff8fce0,
    0x1ff07fe0,
    0x0fe01fc0,
    0x0180ffc0,
    0x0000ffe0,
    0x0000fff0,
    0x0000f0f0,
    0x00008070,
    0x00000030,
    0x00000010,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x27 ''' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x3fe00000,
    0x3fe00000,
    0x3fe00000,
    0x3fe00000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x28 '(' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x0000fc00,
    0x001fffc0,
    0x00fffff0,
    0x03fffffc,
    0x07ffffff,
    0x1fe0003f,
    0x3f000007,
    0x3c000001,
    0x30000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x29 ')' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x30000000,
    0x3c000001,
    0x3f80000f,
    0x0ff0007f,
    0x07ffffff,
    0x01fffffc,
    0x007ffff0,
    0x000fff80,
    0x0000f000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x2a '*' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x06000000,
    0x06300000,
    0x07700000,
    0x03f00000,
    0x3fe00000,
    0x3fc00000,
    0x3fe00000,
    0x03f00000,
    0x07700000,
    0x06300000,
    0x06000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x2b '+' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00003000,
    0x00003000,
    0x00003000,
    0x00003000,
    0x00003000,
    0x00003000,
    0x00003000,
    0x00003000,
    0x003ffff0,
    0x003ffff0,
    0x003ffff0,
    0x003ffff0,
    0x00003000,
    0x00003000,
    0x00003000,
    0x00003000,
    0x00003000,
    0x00003000,
    0x00003000,
    0x00003000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x2c ',' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x000000f1,
    0x000000f3,
    0x000000ff,
    0x000000ff,
    0x000000fc,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x2d '-' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00003000,
    0x00003000,
    0x00003000,
    0x00003000,
    0x00003000,
    0x00003000,
    0x00003000,
    0x00003000,
    0x00003000,
    0x00003000,
    0x00003000,
    0x00003000,
    0x00003000,
    0x00003000,
    0x00003000,
    0x00003000,
    0x00003000,
    0x00003000,
    0x00003000,
    0x00003000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x2e '.' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x000000f0,
    0x000000f0,
    0x000000f0,
    0x000000f0,
    0x000000f0,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x2f '/' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000030,
    0x000001f0,
    0x00000ff0,
    0x0000ffc0,
    0x0007fe00,
    0x003ff000,
    0x01ff8000,
    0x0ffc0000,
    0x3fe00000,
    0x3f000000,
    0x38000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x30 '0' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x000fe000,
    0x00fffe00,
    0x03ffff80,
    0x07ffffc0,
    0x0fffffe0,
    0x1f8003f0,
    0x1e0000f0,
    0x1c000070,
    0x1c000030,
    0x1c000030,
    0x1c000030,
    0x1c000070,
    0x1f0000f0,
    0x0fc003f0,
    0x0fffffe0,
    0x07ffffc0,
    0x03ffff80,
    0x007ffe00,
    0x0007e000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x31 '1' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00c00000,
    0x00c00000,
    0x01c00000,
    0x01c00000,
    0x03c00000,
    0x07c00000,
    0x1ffffff0,
    0x1ffffff0,
    0x1ffffff0,
    0x1ffffff0,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x32 '2' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x000000f0,
    0x01f003f0,
    0x07f007f0,
    0x0ff00ff0,
    0x0ff01ff0,
    0x1f003e70,
    0x1e003c70,
    0x1c007870,
    0x1c00f070,
    0x1c00f070,
    0x1c01e070,
    0x1c01e070,
    0x1e03c070,
    0x1f0fc070,
    0x0fff8070,
    0x0fff0070,
    0x07fe0070,
    0x01fc0070,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x33 '3' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000e00,
    0x01e00f80,
    0x07e00fe0,
    0x0fe00fe0,
    0x0fe00ff0,
    0x1f0000f0,
    0x1e000070,
    0x1c030030,
    0x1c030030,
    0x1c030030,
    0x1c070030,
    0x1c078070,
    0x1e0f8070,
    0x1fffc0f0,
    0x0fffffe0,
    0x0ffdffe0,
    0x07f8ffc0,
    0x00e07f80,
    0x00003e00,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x34 '4' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00003c00,
    0x0000fc00,
    0x0001fc00,
    0x0003fc00,
    0x000fdc00,
    0x001f9c00,
    0x007e1c00,
    0x00fc1c00,
    0x01f01c00,
    0x07e01c00,
    0x0f801c00,
    0x1ffffff0,
    0x1ffffff0,
    0x1ffffff0,
    0x1ffffff0,
    0x00001c00,
    0x00001c00,
    0x00001c00,
    0x00001c00,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x35 '5' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000300,
    0x000383c0,
    0x01ff83e0,
    0x1fff83e0,
    0x1fff83f0,
    0x1ff700f0,
    0x1c0f0070,
    0x1c0e0030,
    0x1c0e0030,
    0x1c0e0030,
    0x1c0e0030,
    0x1c0e0070,
    0x1c0f00f0,
    0x1c0fc1f0,
    0x1c07ffe0,
    0x1c03ffc0,
    0x1c03ffc0,
    0x0000ff00,
    0x00001000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x36 '6' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x0003e000,
    0x007fff00,
    0x01ffff80,
    0x07ffffc0,
    0x0fffffe0,
    0x0fc7c1f0,
    0x1f0700f0,
    0x1e0e0070,
    0x1c0e0030,
    0x1c0e0030,
    0x1c0e0030,
    0x1c0e0070,
    0x1e0f0070,
    0x1f0780f0,
    0x0f87fff0,
    0x0f87ffe0,
    0x0783ffc0,
    0x0180ff80,
    0x00003c00,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x37 '7' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x1c000000,
    0x1c000000,
    0x1c000000,
    0x1c000000,
    0x1c000070,
    0x1c0003f0,
    0x1c001ff0,
    0x1c007ff0,
    0x1c01ffe0,
    0x1c07fe00,
    0x1c1ff000,
    0x1c3f8000,
    0x1c7e0000,
    0x1dfc0000,
    0x1ff00000,
    0x1fc00000,
    0x1f800000,
    0x1f000000,
    0x1c000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x38 '8' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00001e00,
    0x00e07f80,
    0x03f8ffc0,
    0x0ffdffe0,
    0x0ffffff0,
    0x1fbfc0f0,
    0x1e0f8070,
    0x1c078070,
    0x1c070030,
    0x1c070030,
    0x1c070030,
    0x1c078030,
    0x1e0f8070,
    0x1fffc0f0,
    0x0fffffe0,
    0x07fdffe0,
    0x03f8ffc0,
    0x00407f80,
    0x00000800,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x39 '9' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x01ff0380,
    0x07ff83c0,
    0x0fffc3e0,
    0x0fffe3f0,
    0x1f01e0f0,
    0x1e00e070,
    0x1c00e030,
    0x1c007030,
    0x1c007030,
    0x1c006030,
    0x1c00e070,
    0x1e01e1f0,
    0x1f87c7e0,
    0x0fffffc0,
    0x07ffff80,
    0x03ffff00,
    0x00fff800,
    0x00060000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x3a ':' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x007800f0,
    0x007800f0,
    0x007800f0,
    0x007800f0,
    0x007800f0,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x3b ';' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x007800f1,
    0x007800f3,
    0x007800ff,
    0x007800ff,
    0x007800fc,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x3c '<' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00007800,
    0x00007800,
    0x0000f800,
    0x0000fc00,
    0x0000fc00,
    0x0001de00,
    0x0001ce00,
    0x0003cf00,
    0x00038700,
    0x00038700,
    0x00070380,
    0x00070380,
    0x000e03c0,
    0x000e01c0,
    0x001e01c0,
    0x001c00e0,
    0x001c00e0,
    0x001800e0,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x3d '=' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00038700,
    0x00038700,
    0x00038700,
    0x00038700,
    0x00038700,
    0x00038700,
    0x00038700,
    0x00038700,
    0x00038700,
    0x00038700,
    0x00038700,
    0x00038700,
    0x00038700,
    0x00038700,
    0x00038700,
    0x00038700,
    0x00038700,
    0x00038700,
    0x00038700,
    0x00038700,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x3e '>' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x001800e0,
    0x001c00e0,
    0x001c00e0,
    0x001e01c0,
    0x000e01c0,
    0x000e03c0,
    0x00070380,
    0x00070780,
    0x00038700,
    0x00038700,
    0x0003ce00,
    0x0001ce00,
    0x0001de00,
    0x0000fc00,
    0x0000fc00,
    0x0000f800,
    0x00007800,
    0x00007800,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x3f '?' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x01c00000,
    0x07c00000,
    0x0fc00000,
    0x1fc00000,
    0x1f000000,
    0x3c000000,
    0x380078f0,
    0x3800f8f0,
    0x3801f8f0,
    0x3803f8f0,
    0x3807c000,
    0x3c1f8000,
    0x1fff0000,
    0x1ffe0000,
    0x0ffc0000,
    0x07f00000,
    0x00c00000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x40 '@' */
    0x00000000,
    0x00000000,
    0x00018000,
    0x001ff800,
    0x007ffe00,
    0x01ffff80,
    0x03f80fc0,
    0x07c003e0,
    0x0783fce0,
    0x0e0ffe70,
    0x0e1fff70,
    0x1c3fff38,
    0x1c7e0738,
    0x18780718,
    0x18700718,
    0x18600718,
    0x18e00e18,
    0x18f01e18,
    0x1878ff18,
    0x187fff18,
    0x1c3fff18,
    0x1c7fe338,
    0x0e7e0770,
    0x0f600e70,
    0x07801ee0,
    0x03f0fdc0,
    0x01fff9c0,
    0x00fff100,
    0x003f8000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x41 'A' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000030,
    0x000001f0,
    0x00000ff0,
    0x00003ff0,
    0x0001ffe0,
    0x000fff00,
    0x003ff800,
    0x01fff800,
    0x0fff7800,
    0x3ff87800,
    0x3fc07800,
    0x3e007800,
    0x3fc07800,
    0x3ff87800,
    0x1fff7800,
    0x03fff800,
    0x007ffc00,
    0x001fff80,
    0x0003ffe0,
    0x00007ff0,
    0x00000ff0,
    0x000003f0,
    0x00000070,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x42 'B' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x38070070,
    0x38070070,
    0x38070070,
    0x38070070,
    0x38070070,
    0x38070070,
    0x38070070,
    0x38070070,
    0x3c070070,
    0x3c0f80f0,
    0x1f3f80f0,
    0x1fffc1e0,
    0x1fffffe0,
    0x0ff9ffc0,
    0x03f0ffc0,
    0x00007f00,
    0x00001c00,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x43 'C' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x007ff800,
    0x01fffe00,
    0x03ffff80,
    0x0fffffc0,
    0x0ff01fe0,
    0x1f8003e0,
    0x1e0001f0,
    0x3c0000f0,
    0x3c000070,
    0x38000070,
    0x38000070,
    0x38000030,
    0x38000030,
    0x38000070,
    0x38000070,
    0x38000070,
    0x3c0000f0,
    0x1e0001e0,
    0x1f8007e0,
    0x0fc03fc0,
    0x07c03f80,
    0x03c03f00,
    0x00c03c00,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x44 'D' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x38000070,
    0x38000070,
    0x38000070,
    0x38000070,
    0x38000070,
    0x38000070,
    0x38000070,
    0x38000070,
    0x3c000070,
    0x1c0000f0,
    0x1e0000f0,
    0x1f0001e0,
    0x0f8007e0,
    0x0ff87fc0,
    0x07ffff80,
    0x03ffff00,
    0x00fffc00,
    0x003ff000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x45 'E' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x38070070,
    0x38070070,
    0x38070070,
    0x38070070,
    0x38070070,
    0x38070070,
    0x38070070,
    0x38070070,
    0x38070070,
    0x38070070,
    0x38070070,
    0x38070070,
    0x38070070,
    0x38070070,
    0x38000070,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x46 'F' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x38070000,
    0x38070000,
    0x38070000,
    0x38070000,
    0x38070000,
    0x38070000,
    0x38070000,
    0x38070000,
    0x38070000,
    0x38070000,
    0x38070000,
    0x38070000,
    0x38000000,
    0x38000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x47 'G' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x003ff800,
    0x00fffe00,
    0x03ffff00,
    0x07ffffc0,
    0x0ff83fc0,
    0x1f8007e0,
    0x1f0001f0,
    0x3e0000f0,
    0x3c000070,
    0x38000070,
    0x38000070,
    0x38000030,
    0x38038030,
    0x38038030,
    0x38038070,
    0x38038070,
    0x3c038070,
    0x3e0380e0,
    0x1f0381e0,
    0x1fc387c0,
    0x0fc3ffc0,
    0x07c3fff0,
    0x03c3fff0,
    0x00c3fff0,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x48 'H' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x00070000,
    0x00070000,
    0x00070000,
    0x00070000,
    0x00070000,
    0x00070000,
    0x00070000,
    0x00070000,
    0x00070000,
    0x00070000,
    0x00070000,
    0x00070000,
    0x00070000,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x49 'I' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x4a 'J' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000fc0,
    0x00000fe0,
    0x00000fe0,
    0x00000ff0,
    0x00000070,
    0x00000070,
    0x00000030,
    0x00000030,
    0x00000030,
    0x00000070,
    0x000001f0,
    0x3ffffff0,
    0x3fffffe0,
    0x3fffffc0,
    0x3fffff80,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x4b 'K' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x0007e000,
    0x000fc000,
    0x001f8000,
    0x003f0000,
    0x007f8000,
    0x00ffc000,
    0x01fff000,
    0x03f3f800,
    0x07e1fe00,
    0x0fc0ff00,
    0x1f803f80,
    0x3f001fe0,
    0x3e0007f0,
    0x3c0003f0,
    0x380001f0,
    0x30000070,
    0x20000030,
    0x00000010,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x4c 'L' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x00000070,
    0x00000070,
    0x00000070,
    0x00000070,
    0x00000070,
    0x00000070,
    0x00000070,
    0x00000070,
    0x00000070,
    0x00000070,
    0x00000070,
    0x00000070,
    0x00000070,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x4d 'M' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x3fe00000,
    0x3ffc0000,
    0x07ff8000,
    0x00fff000,
    0x001ffe00,
    0x0003ffc0,
    0x00007ff0,
    0x00000ff0,
    0x000003f0,
    0x00000ff0,
    0x00007ff0,
    0x0003ffc0,
    0x001ffe00,
    0x00fff000,
    0x07ff8000,
    0x3ffc0000,
    0x3fe00000,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x00000000,

    /* 0x4e 'N' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x3f800000,
    0x1fe00000,
    0x07f00000,
    0x03fc0000,
    0x00ff0000,
    0x003f8000,
    0x001fe000,
    0x0007f000,
    0x0001fc00,
    0x0000ff00,
    0x00003f80,
    0x00001fe0,
    0x000007f0,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x4f 'O' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00030000,
    0x007ff800,
    0x01fffe00,
    0x03ffff00,
    0x07ffff80,
    0x0ff01fc0,
    0x1f8007e0,
    0x1e0001e0,
    0x3c0000f0,
    0x3c0000f0,
    0x38000070,
    0x38000070,
    0x38000070,
    0x38000030,
    0x38000070,
    0x38000070,
    0x38000070,
    0x3c0000f0,
    0x3c0000f0,
    0x1e0001e0,
    0x1f8007e0,
    0x0fe01fc0,
    0x07ffff80,
    0x03ffff00,
    0x01fffe00,
    0x007ff800,
    0x00030000,
    0x00000000,

    /* 0x50 'P' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x38038000,
    0x38038000,
    0x38038000,
    0x38038000,
    0x38038000,
    0x38038000,
    0x38038000,
    0x38038000,
    0x3c038000,
    0x3c078000,
    0x1f0f8000,
    0x1fff0000,
    0x0fff0000,
    0x07fe0000,
    0x03f80000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x51 'Q' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00030000,
    0x007ff800,
    0x01fffe00,
    0x03ffff00,
    0x07ffff80,
    0x0ff01fc0,
    0x1f8007e0,
    0x1e0001e0,
    0x3c0000f0,
    0x3c0000f0,
    0x38000070,
    0x38000070,
    0x38000070,
    0x38000030,
    0x38000230,
    0x38000770,
    0x380007f0,
    0x3c0007f0,
    0x3c0003f0,
    0x1e0001e0,
    0x1f8007f0,
    0x0fe01ff0,
    0x07fffff8,
    0x03ffff3c,
    0x01fffe3c,
    0x007ff818,
    0x00030000,
    0x00000000,

    /* 0x52 'R' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x38038000,
    0x38038000,
    0x38038000,
    0x38038000,
    0x38038000,
    0x38038000,
    0x38038000,
    0x38038000,
    0x38038000,
    0x38038000,
    0x3c07c000,
    0x1e0ff000,
    0x1ffffff0,
    0x1ffefff0,
    0x0ffcfff0,
    0x07f83ff0,
    0x00e00010,
    0x00000010,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x53 'S' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000f00,
    0x03f80f80,
    0x0ffc0fc0,
    0x1ffe0fe0,
    0x1ffe03f0,
    0x3e1e00f0,
    0x3c0f0070,
    0x380f0070,
    0x380f0070,
    0x38070030,
    0x38078030,
    0x38078030,
    0x38078030,
    0x38078070,
    0x3c03c070,
    0x3e03c0f0,
    0x1fc3e1f0,
    0x1fc1ffe0,
    0x0fc1ffc0,
    0x03c0ffc0,
    0x00403f00,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x54 'T' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x38000000,
    0x38000000,
    0x38000000,
    0x38000000,
    0x38000000,
    0x38000000,
    0x38000000,
    0x38000000,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x38000000,
    0x38000000,
    0x38000000,
    0x38000000,
    0x38000000,
    0x38000000,
    0x38000000,
    0x38000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x55 'U' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x3ffffe00,
    0x3fffff80,
    0x3fffffc0,
    0x3fffffe0,
    0x000007e0,
    0x000001f0,
    0x000000f0,
    0x00000070,
    0x00000070,
    0x00000030,
    0x00000030,
    0x00000070,
    0x00000070,
    0x00000070,
    0x000000f0,
    0x000001f0,
    0x00000fe0,
    0x3fffffe0,
    0x3fffffc0,
    0x3fffff80,
    0x3ffffe00,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x56 'V' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x30000000,
    0x3e000000,
    0x3fc00000,
    0x3ff80000,
    0x3ffe0000,
    0x07ffc000,
    0x00fff800,
    0x001fff00,
    0x0003ffc0,
    0x00007ff0,
    0x00000ff0,
    0x000001f0,
    0x00000ff0,
    0x00007ff0,
    0x0003ffc0,
    0x001fff00,
    0x00fff800,
    0x07ffc000,
    0x3ffe0000,
    0x3ff80000,
    0x3fc00000,
    0x3e000000,
    0x38000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x57 'W' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x3c000000,
    0x3fc00000,
    0x3ff80000,
    0x3fff8000,
    0x1ffff800,
    0x00ffff80,
    0x000ffff0,
    0x0000fff0,
    0x00000ff0,
    0x00001ff0,
    0x0001fff0,
    0x000fffc0,
    0x00fffc00,
    0x0fffe000,
    0x3ffe0000,
    0x3ff00000,
    0x3f000000,
    0x3ff00000,
    0x3fff0000,
    0x07ffe000,
    0x00fffe00,
    0x000fffc0,
    0x0000fff0,
    0x00001ff0,
    0x00000ff0,
    0x0000fff0,
    0x001ffff0,
    0x01ffff00,

    /* 0x58 'X' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x20000010,
    0x30000070,
    0x380000f0,
    0x3e0001f0,
    0x3f0007f0,
    0x3fc00fe0,
    0x0fe03fc0,
    0x07f87f00,
    0x01fdfe00,
    0x00fff800,
    0x003ff000,
    0x001fc000,
    0x003ff000,
    0x007ff800,
    0x01fdfe00,
    0x03f87f00,
    0x0fe03fc0,
    0x1fc00fe0,
    0x3f8007f0,
    0x3e0003f0,
    0x3c0000f0,
    0x30000070,
    0x20000010,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x59 'Y' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x30000000,
    0x38000000,
    0x3e000000,
    0x3f000000,
    0x3fc00000,
    0x0ff00000,
    0x07f80000,
    0x01fe0000,
    0x007f0000,
    0x003ffff0,
    0x000ffff0,
    0x0003fff0,
    0x000ffff0,
    0x003ffff0,
    0x007f0000,
    0x01fe0000,
    0x07f80000,
    0x0ff00000,
    0x3fc00000,
    0x3f800000,
    0x3e000000,
    0x38000000,
    0x30000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x5a 'Z' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x000000f0,
    0x380001f0,
    0x380003f0,
    0x380007f0,
    0x38001ff0,
    0x38003ff0,
    0x38007e70,
    0x3801fc70,
    0x3803f870,
    0x3807f070,
    0x380fc070,
    0x383f8070,
    0x387f0070,
    0x38fe0070,
    0x39f80070,
    0x3ff00070,
    0x3fe00070,
    0x3fc00070,
    0x3f000070,
    0x3e000070,
    0x3c000070,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x5b '[' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x3fffffff,
    0x3fffffff,
    0x3fffffff,
    0x3fffffff,
    0x38000000,
    0x38000000,
    0x38000000,
    0x38000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x5c '\' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x30000000,
    0x3e000000,
    0x3fc00000,
    0x0ffc0000,
    0x01ff8000,
    0x003ff000,
    0x0007fe00,
    0x0000ffc0,
    0x00001ff0,
    0x000003f0,
    0x00000070,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x5d ']' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x38000000,
    0x38000000,
    0x38000000,
    0x38000000,
    0x3fffffff,
    0x3fffffff,
    0x3fffffff,
    0x3fffffff,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x5e '^' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x02000000,
    0x0e000000,
    0x1e000000,
    0x3e000000,
    0x38000000,
    0x30000000,
    0x3c000000,
    0x3e000000,
    0x1e000000,
    0x0e000000,
    0x02000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x5f '_' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000003,
    0x00000003,
    0x00000003,
    0x00000003,
    0x00000003,
    0x00000003,
    0x00000003,
    0x00000003,
    0x00000003,
    0x00000003,
    0x00000003,
    0x00000003,
    0x00000003,
    0x00000003,
    0x00000003,
    0x00000003,
    0x00000003,
    0x00000003,
    0x00000003,
    0x00000003,
    0x00000003,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x60 '`' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x20000000,
    0x30000000,
    0x38000000,
    0x3c000000,
    0x3e000000,
    0x1e000000,
    0x06000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x61 'a' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000780,
    0x00061fc0,
    0x001e3fe0,
    0x003e3ff0,
    0x007e7ff0,
    0x007c7870,
    0x00707030,
    0x00707030,
    0x00607030,
    0x00607070,
    0x00706070,
    0x007060e0,
    0x0078e1e0,
    0x007fffe0,
    0x003ffff0,
    0x001ffff0,
    0x000ffff0,
    0x00000030,
    0x00000030,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x62 'b' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x003f03e0,
    0x007800f0,
    0x00700070,
    0x00700030,
    0x00700030,
    0x00700030,
    0x00700070,
    0x00780070,
    0x007c01f0,
    0x003fffe0,
    0x003fffc0,
    0x001fff80,
    0x0007fe00,
    0x00006000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x63 'c' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x0000fc00,
    0x0007ff80,
    0x001fffc0,
    0x003fffe0,
    0x003f87e0,
    0x007c00f0,
    0x00780070,
    0x00700030,
    0x00700030,
    0x00700030,
    0x00700070,
    0x00700070,
    0x007c01f0,
    0x007f07e0,
    0x003f07e0,
    0x001f07c0,
    0x00070700,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x64 'd' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x0000f800,
    0x0007ff00,
    0x001fffc0,
    0x003fffe0,
    0x003fffe0,
    0x007c01f0,
    0x00780070,
    0x00700070,
    0x00700030,
    0x00700030,
    0x00700070,
    0x00780070,
    0x003c00f0,
    0x001f83e0,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x65 'e' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00003000,
    0x0007ff00,
    0x000fffc0,
    0x001fffe0,
    0x003fffe0,
    0x007c71f0,
    0x00787070,
    0x00707070,
    0x00707030,
    0x00707030,
    0x00707030,
    0x00707070,
    0x00787070,
    0x007c70f0,
    0x003ff3f0,
    0x001ff3e0,
    0x000ff3c0,
    0x0003f300,
    0x00003000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x66 'f' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00700000,
    0x00700000,
    0x0ffffff0,
    0x1ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x38700000,
    0x38700000,
    0x38700000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x67 'g' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00007000,
    0x0007ff03,
    0x001fff83,
    0x003fffc3,
    0x003fffe3,
    0x007c01f1,
    0x007800f0,
    0x00700070,
    0x00700030,
    0x00700030,
    0x00700030,
    0x00700070,
    0x007800f0,
    0x003f03e3,
    0x007fffff,
    0x007fffff,
    0x007fffff,
    0x007ffffc,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x68 'h' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x001f8000,
    0x003c0000,
    0x00780000,
    0x00700000,
    0x00700000,
    0x00700000,
    0x00700000,
    0x00700000,
    0x007ffff0,
    0x007ffff0,
    0x003ffff0,
    0x001ffff0,
    0x0003fff0,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x69 'i' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x3c7ffff0,
    0x3c7ffff0,
    0x3c7ffff0,
    0x3c7ffff0,
    0x3c7ffff0,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x6a 'j' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x3c7fffff,
    0x3c7fffff,
    0x3c7fffff,
    0x3c7fffff,
    0x3c7fffff,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x6b 'k' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x0000f800,
    0x0001f000,
    0x0003e000,
    0x0007f800,
    0x000ffc00,
    0x001fff00,
    0x003e3fc0,
    0x007c1fe0,
    0x007807f0,
    0x007001f0,
    0x006000f0,
    0x00400030,
    0x00000010,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x6c 'l' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x6d 'm' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x007ffff0,
    0x007ffff0,
    0x007ffff0,
    0x007ffff0,
    0x003f8000,
    0x003c0000,
    0x00780000,
    0x00700000,
    0x00700000,
    0x00700000,
    0x00700000,
    0x007ffff0,
    0x007ffff0,
    0x003ffff0,
    0x001ffff0,
    0x003ffff0,
    0x00780000,
    0x00700000,
    0x00700000,
    0x00700000,
    0x00700000,
    0x00780000,
    0x007ffff0,
    0x007ffff0,
    0x003ffff0,
    0x001ffff0,
    0x0007fff0,

    /* 0x6e 'n' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x007ffff0,
    0x007ffff0,
    0x007ffff0,
    0x007ffff0,
    0x003f8000,
    0x003c0000,
    0x00780000,
    0x00700000,
    0x00700000,
    0x00700000,
    0x00700000,
    0x00780000,
    0x007ffff0,
    0x007ffff0,
    0x003ffff0,
    0x001ffff0,
    0x0003fff0,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x6f 'o' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x0000f800,
    0x0007ff00,
    0x000fff80,
    0x001fffc0,
    0x003fffe0,
    0x007c01f0,
    0x007800f0,
    0x00700070,
    0x00700030,
    0x00600030,
    0x00700030,
    0x00700070,
    0x007800f0,
    0x007c01f0,
    0x003fffe0,
    0x001fffc0,
    0x000fff80,
    0x0007ff00,
    0x00007000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x70 'p' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x007fffff,
    0x007fffff,
    0x007fffff,
    0x007fffff,
    0x003f03e0,
    0x007800f0,
    0x00700070,
    0x00700030,
    0x00700030,
    0x00700030,
    0x00700070,
    0x00780070,
    0x007c01f0,
    0x003fffe0,
    0x003fffc0,
    0x001fff80,
    0x0007fe00,
    0x00006000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x71 'q' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x0000f800,
    0x0007ff00,
    0x001fffc0,
    0x003fffe0,
    0x003fffe0,
    0x007c01f0,
    0x00780070,
    0x00700070,
    0x00700030,
    0x00700030,
    0x00700070,
    0x00780070,
    0x003c00f0,
    0x001f83e0,
    0x007fffff,
    0x007fffff,
    0x007fffff,
    0x007fffff,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x72 'r' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x007ffff0,
    0x007ffff0,
    0x007ffff0,
    0x007ffff0,
    0x003e0000,
    0x00780000,
    0x00780000,
    0x00700000,
    0x00700000,
    0x00700000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x73 's' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000300,
    0x000f83c0,
    0x003fc3e0,
    0x003fe3e0,
    0x007fe0f0,
    0x0070e070,
    0x0070f070,
    0x0070f030,
    0x00607030,
    0x00707030,
    0x00707870,
    0x00787870,
    0x007e3ff0,
    0x003e3fe0,
    0x001e1fe0,
    0x000e1fc0,
    0x00000600,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x74 't' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00700000,
    0x00700000,
    0x0fffffe0,
    0x0ffffff0,
    0x0ffffff0,
    0x0ffffff0,
    0x00700070,
    0x00700030,
    0x00700030,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x75 'u' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x007fff00,
    0x007fffc0,
    0x007fffe0,
    0x007ffff0,
    0x007ffff0,
    0x00000070,
    0x00000030,
    0x00000030,
    0x00000030,
    0x00000070,
    0x00000070,
    0x000001e0,
    0x007fffc0,
    0x007ffff0,
    0x007ffff0,
    0x007ffff0,
    0x007ffff0,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x76 'v' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00400000,
    0x00700000,
    0x007e0000,
    0x007fc000,
    0x007ff800,
    0x001ffe00,
    0x0003ffc0,
    0x00007ff0,
    0x000007f0,
    0x000001f0,
    0x00000ff0,
    0x00007ff0,
    0x0003ff80,
    0x001ffe00,
    0x007ff000,
    0x007f8000,
    0x007e0000,
    0x00700000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x77 'w' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00700000,
    0x007f0000,
    0x007ff000,
    0x007ffe00,
    0x000fffe0,
    0x0000fff0,
    0x00000ff0,
    0x00000ff0,
    0x0000fff0,
    0x000fffe0,
    0x007ffe00,
    0x007fe000,
    0x007f0000,
    0x007fe000,
    0x007ffe00,
    0x000fffe0,
    0x0000fff0,
    0x00000ff0,
    0x00000ff0,
    0x0000fff0,
    0x000fffc0,
    0x007ffe00,
    0x007fe000,
    0x007f0000,
    0x00700000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x78 'x' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00400030,
    0x00700070,
    0x007801f0,
    0x007e03f0,
    0x007f07f0,
    0x003fdfc0,
    0x000fff80,
    0x0007fe00,
    0x0001fc00,
    0x0003fe00,
    0x000fff80,
    0x001fdfc0,
    0x007f07f0,
    0x007e03f0,
    0x007801f0,
    0x00700070,
    0x00400030,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x79 'y' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00700000,
    0x007e0000,
    0x007fc000,
    0x007ff000,
    0x001ffe00,
    0x0003ffc3,
    0x00007fff,
    0x00000fff,
    0x000001ff,
    0x00000ffc,
    0x00007ff0,
    0x0003ffc0,
    0x001ffe00,
    0x007ff000,
    0x007fc000,
    0x007e0000,
    0x00700000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x7a 'z' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000070,
    0x007000f0,
    0x007001f0,
    0x007007f0,
    0x00700ff0,
    0x00701ff0,
    0x00703f70,
    0x00707e70,
    0x0071fc70,
    0x0073f070,
    0x0077e070,
    0x007fc070,
    0x007f8070,
    0x007e0070,
    0x007c0070,
    0x00780070,
    0x00000070,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x7b '{' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00007000,
    0x0000f800,
    0x07fffffe,
    0x1fffdfff,
    0x1fffcfff,
    0x3fff07ff,
    0x38000001,
    0x30000000,
    0x30000000,
    0x30000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x7c '|' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x3ffffff0,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x7d '}' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x30000000,
    0x30000000,
    0x3c000001,
    0x3fff07ff,
    0x1fffdfff,
    0x1fffdfff,
    0x03fffffe,
    0x0000f800,
    0x00007000,
    0x00007000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x7e '~' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00001000,
    0x00003800,
    0x00007800,
    0x00007000,
    0x0000e000,
    0x0000e000,
    0x00006000,
    0x00007000,
    0x00007000,
    0x00003800,
    0x00003800,
    0x00003800,
    0x00001800,
    0x00001800,
    0x00003800,
    0x0000f800,
    0x0000f000,
    0x00002000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x7f '' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x3ffffff8,
    0x3ffffff8,
    0x3ffffff8,
    0x30000018,
    0x30000018,
    0x30000018,
    0x30000018,
    0x3ffffff8,
    0x3ffffff8,
    0x3ffffff8,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
#else
    /* 0x20 ' ' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x21 '!' */
    0x00000000,
    0x00000000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x01C00000,
    0x01C00000,
    0x01C00000,
    0x01C00000,
    0x01C00000,
    0x01C00000,
    0x01C00000,
    0x01C00000,
    0x00000000,
    0x00000000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x22 '"' */
    0x00000000,
    0x00000000,
    0x039C0000,
    0x039C0000,
    0x039C0000,
    0x039C0000,
    0x039C0000,
    0x039C0000,
    0x039C0000,
    0x039C0000,
    0x039C0000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x23 '#' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x003C7000,
    0x00387000,
    0x00387000,
    0x0038F000,
    0x0078F000,
    0x0078F000,
    0x0078F000,
    0x0070E000,
    0x07FFFE00,
    0x07FFFE00,
    0x0071E000,
    0x0071E000,
    0x00F1E000,
    0x00F1E000,
    0x00E1C000,
    0x0FFFFC00,
    0x0FFFFC00,
    0x0FFFFC00,
    0x00E3C000,
    0x01E3C000,
    0x01E38000,
    0x01E38000,
    0x01C38000,
    0x01C38000,
    0x01C38000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x24 '$' */
    0x000E0000,
    0x000E0000,
    0x001E0000,
    0x00FFC000,
    0x01FFF000,
    0x03EFF800,
    0x07CE7800,
    0x078E3C00,
    0x078E3C00,
    0x078E0000,
    0x078E0000,
    0x078E0000,
    0x07EE0000,
    0x03FE0000,
    0x01FF8000,
    0x007FF000,
    0x000FF800,
    0x000FFC00,
    0x000E7C00,
    0x000E3E00,
    0x0F0E3E00,
    0x0F0E3E00,
    0x0F0E3E00,
    0x0F8E3C00,
    0x078E3C00,
    0x07EEF800,
    0x03FFF000,
    0x00FFE000,
    0x000E0000,
    0x000E0000,
    0x000E0000,
    0x000E0000,

    /* 0x25 '%' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x07E00380,
    0x1FF00700,
    0x1FF80600,
    0x3C3C0E00,
    0x381C1C00,
    0x781E1C00,
    0x781E3800,
    0x381C3800,
    0x3C3C7000,
    0x3E7C7000,
    0x1FF8E000,
    0x0FF0E000,
    0x0001C000,
    0x000383F0,
    0x00038FF8,
    0x00071FFC,
    0x00071E1E,
    0x000E1C0E,
    0x000E3C0E,
    0x001C3C0E,
    0x001C3C0E,
    0x00381E1E,
    0x00701F3C,
    0x00700FFC,
    0x00E003F0,
    0x00000000,
    0x00000000,
    0x00000000,


    /* 0x26 '&' */
    0x00000000,
    0x00000000,
    0x000F8000,
    0x003FE000,
    0x007FF000,
    0x00F9F000,
    0x00F0F000,
    0x00F0F800,
    0x00F0F800,
    0x00F0F000,
    0x00F9F000,
    0x007FE000,
    0x007FC000,
    0x003F8000,
    0x007F0000,
    0x01FF8000,
    0x03F7CF80,
    0x03E7EF00,
    0x07C3EF00,
    0x0781FF00,
    0x0780FE00,
    0x07807E00,
    0x07803E00,
    0x07803E00,
    0x07C07F00,
    0x03F1FF80,
    0x01FFE7C0,
    0x00FF83E0,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x27 ''' */
    0x00000000,
    0x00000000,
    0x07800000,
    0x07800000,
    0x07800000,
    0x07800000,
    0x07800000,
    0x07800000,
    0x07800000,
    0x07800000,
    0x07800000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x28 '(' */
    0x00000000,
    0x00000000,
    0x001C0000,
    0x003C0000,
    0x00380000,
    0x00780000,
    0x00F00000,
    0x00F00000,
    0x01E00000,
    0x01E00000,
    0x01E00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x01E00000,
    0x01E00000,
    0x00E00000,
    0x00F00000,
    0x00700000,
    0x00780000,

    /* 0x29 ')' */
    0x00000000,
    0x00000000,
    0x07000000,
    0x07000000,
    0x03800000,
    0x03C00000,
    0x01C00000,
    0x01E00000,
    0x01E00000,
    0x00F00000,
    0x00F00000,
    0x00F00000,
    0x00780000,
    0x00780000,
    0x00780000,
    0x00780000,
    0x007C0000,
    0x007C0000,
    0x007C0000,
    0x007C0000,
    0x00780000,
    0x00780000,
    0x00780000,
    0x00780000,
    0x00780000,
    0x00F00000,
    0x00F00000,
    0x00F00000,
    0x01E00000,
    0x01E00000,
    0x01C00000,
    0x03C00000,

    /* 0x2A '*' */
    0x00000000,
    0x00000000,
    0x00700000,
    0x00700000,
    0x00700000,
    0x07770000,
    0x07FF0000,
    0x01FC0000,
    0x00F80000,
    0x01FC0000,
    0x03DE0000,
    0x038E0000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x2B '+' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x000F0000,
    0x000F0000,
    0x000F0000,
    0x000F0000,
    0x000F0000,
    0x000F0000,
    0x000F0000,
    0x000F0000,
    0x0FFFFF00,
    0x0FFFFF00,
    0x000F0000,
    0x000F0000,
    0x000F0000,
    0x000F0000,
    0x000F0000,
    0x000F0000,
    0x000F0000,
    0x000F0000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x2C ',' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x00E00000,
    0x00E00000,
    0x01C00000,
    0x03C00000,

    /* 0x2D '-' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x0FFFFF00,
    0x0FFFFF00,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x2E '.' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x2F '/' */
    0x00000000,
    0x00000000,
    0x001C0000,
    0x001C0000,
    0x003C0000,
    0x00380000,
    0x00380000,
    0x00780000,
    0x00700000,
    0x00700000,
    0x00F00000,
    0x00E00000,
    0x00E00000,
    0x01E00000,
    0x01C00000,
    0x01C00000,
    0x03C00000,
    0x03800000,
    0x03800000,
    0x03800000,
    0x07000000,
    0x07000000,
    0x07000000,
    0x0E000000,
    0x0E000000,
    0x0E000000,
    0x1C000000,
    0x1C000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x30 '0' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x007F8000,
    0x00FFE000,
    0x01FFF000,
    0x03E0F800,
    0x03C0F800,
    0x07C07800,
    0x07807C00,
    0x07803C00,
    0x07803C00,
    0x0F803C00,
    0x0F803E00,
    0x0F803E00,
    0x0F803E00,
    0x0F803E00,
    0x0F803E00,
    0x0F803E00,
    0x07803C00,
    0x07803C00,
    0x07803C00,
    0x07C07C00,
    0x03C07800,
    0x03E0F800,
    0x01F1F000,
    0x00FFE000,
    0x007FC000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x31 '1' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00078000,
    0x00078000,
    0x000F8000,
    0x001F8000,
    0x007F8000,
    0x01FF8000,
    0x01FF8000,
    0x00078000,
    0x00078000,
    0x00078000,
    0x00078000,
    0x00078000,
    0x00078000,
    0x00078000,
    0x00078000,
    0x00078000,
    0x00078000,
    0x00078000,
    0x00078000,
    0x00078000,
    0x00078000,
    0x00078000,
    0x00078000,
    0x00078000,
    0x00078000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x32 '2' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x007FC000,
    0x01FFF000,
    0x03FFF800,
    0x03E0F800,
    0x07C07C00,
    0x07803C00,
    0x07803C00,
    0x07803C00,
    0x07803C00,
    0x00007C00,
    0x00007C00,
    0x0000F800,
    0x0003F000,
    0x000FE000,
    0x001FC000,
    0x007F0000,
    0x00FC0000,
    0x01F00000,
    0x03E00000,
    0x07C00000,
    0x07800000,
    0x0F800000,
    0x0FFFFC00,
    0x0FFFFC00,
    0x0FFFFC00,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x33 '3' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x007FC000,
    0x01FFF000,
    0x03FFF800,
    0x03E0F800,
    0x07C07800,
    0x07807C00,
    0x07807C00,
    0x07807C00,
    0x00007800,
    0x0000F800,
    0x0003F000,
    0x001FE000,
    0x001FF000,
    0x0001F800,
    0x00007C00,
    0x00003E00,
    0x00003E00,
    0x0F803E00,
    0x0F803E00,
    0x0F803E00,
    0x07803C00,
    0x07C07C00,
    0x03E1F800,
    0x03FFF000,
    0x00FFC000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x34 '4' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x0001E000,
    0x0003E000,
    0x0007E000,
    0x0007E000,
    0x000FE000,
    0x001FE000,
    0x003DE000,
    0x003DE000,
    0x0079E000,
    0x00F1E000,
    0x00F1E000,
    0x01E1E000,
    0x03C1E000,
    0x07C1E000,
    0x0781E000,
    0x0F01E000,
    0x0FFFFE00,
    0x0FFFFE00,
    0x0FFFFE00,
    0x0001E000,
    0x0001E000,
    0x0001E000,
    0x0001E000,
    0x0001E000,
    0x0001E000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x35 '5' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x01FFF800,
    0x01FFF800,
    0x01FFF800,
    0x01C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03BFC000,
    0x03FFE000,
    0x07FFF800,
    0x07E0F800,
    0x07807C00,
    0x00007C00,
    0x00003C00,
    0x00003E00,
    0x00003C00,
    0x00003C00,
    0x0F803C00,
    0x0F807C00,
    0x07C0F800,
    0x07E1F800,
    0x03FFE000,
    0x00FFC000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x36 '6' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x003FC000,
    0x00FFF000,
    0x01FFF800,
    0x01F0F800,
    0x03E07C00,
    0x03C03C00,
    0x07C00000,
    0x07800000,
    0x07800000,
    0x079F8000,
    0x07FFF000,
    0x0FFFF800,
    0x0FE0F800,
    0x0FC07C00,
    0x0FC03C00,
    0x0F803E00,
    0x07803E00,
    0x07803E00,
    0x07803E00,
    0x07803C00,
    0x07C03C00,
    0x03E07C00,
    0x01F1F800,
    0x00FFF000,
    0x007FE000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x37 '7' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x0FFFFE00,
    0x0FFFFE00,
    0x0FFFFE00,
    0x00003C00,
    0x00007C00,
    0x00007800,
    0x0000F000,
    0x0001E000,
    0x0003E000,
    0x0003C000,
    0x0007C000,
    0x00078000,
    0x000F0000,
    0x000F0000,
    0x001E0000,
    0x001E0000,
    0x003E0000,
    0x003C0000,
    0x003C0000,
    0x007C0000,
    0x00780000,
    0x00780000,
    0x00F80000,
    0x00F80000,
    0x00F00000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x38 '8' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x007FC000,
    0x01FFE000,
    0x01FFF000,
    0x03E0F800,
    0x03C07800,
    0x07C07800,
    0x07807C00,
    0x07C07800,
    0x03C07800,
    0x03E0F800,
    0x01FFF000,
    0x00FFE000,
    0x01FFF000,
    0x03F1F800,
    0x07C07C00,
    0x07803C00,
    0x0F803C00,
    0x0F803E00,
    0x0F803C00,
    0x0F803C00,
    0x07803C00,
    0x07C07C00,
    0x03F0F800,
    0x01FFF000,
    0x00FFC000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x39 '9' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x007FC000,
    0x01FFE000,
    0x03FFF000,
    0x03E0F800,
    0x07C07800,
    0x07807C00,
    0x07803C00,
    0x07803C00,
    0x07803C00,
    0x07803C00,
    0x07807E00,
    0x07807E00,
    0x07C0FC00,
    0x03F1FC00,
    0x01FFFC00,
    0x00FFBC00,
    0x000C3C00,
    0x00003C00,
    0x00007800,
    0x07807800,
    0x0780F800,
    0x07C0F000,
    0x03E1E000,
    0x01FFC000,
    0x00FF8000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x3A ':' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x3B ';' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x00E00000,
    0x00E00000,
    0x01C00000,
    0x03C00000,

    /* 0x3C '<' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00001E00,
    0x00007E00,
    0x0001FC00,
    0x000FF000,
    0x003F8000,
    0x01FE0000,
    0x07F80000,
    0x07C00000,
    0x07E00000,
    0x07F80000,
    0x00FE0000,
    0x003FC000,
    0x000FF000,
    0x0001FE00,
    0x00007E00,
    0x00000E00,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x3D '=' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x0FFFFF00,
    0x0FFFFF00,
    0x0FFFFF00,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x0FFFFF00,
    0x0FFFFF00,
    0x0FFFFF00,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x3E '>' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x07800000,
    0x07E00000,
    0x03F80000,
    0x00FF0000,
    0x001FC000,
    0x0007F800,
    0x0001FE00,
    0x00003E00,
    0x00007E00,
    0x0001FE00,
    0x000FF000,
    0x003FC000,
    0x00FE0000,
    0x07F80000,
    0x07E00000,
    0x07000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x3F '?' */
    0x00000000,
    0x00000000,
    0x003F8000,
    0x00FFE000,
    0x01FFF000,
    0x03E0F800,
    0x03C07800,
    0x07C07800,
    0x07807C00,
    0x07807C00,
    0x00007800,
    0x0000F800,
    0x0000F000,
    0x0001F000,
    0x0003E000,
    0x0007C000,
    0x000F8000,
    0x001F0000,
    0x001E0000,
    0x001E0000,
    0x001E0000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x001E0000,
    0x001E0000,
    0x001E0000,
    0x001E0000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x40 '@' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x001FFC00,
    0x007FFF00,
    0x01F80F80,
    0x03E003C0,
    0x078001E0,
    0x0780C0F0,
    0x0F0FF770,
    0x0E1FFF78,
    0x1E3E7E78,
    0x1E7C3E38,
    0x1C781E38,
    0x1CF81E38,
    0x3CF01C38,
    0x3CF03C78,
    0x1CF03C70,
    0x1CF03C70,
    0x1CF078F0,
    0x1EF0F9E0,
    0x0EFFFBC0,
    0x0F7FFF80,
    0x073F3E70,
    0x078000E0,
    0x03E003E0,
    0x01F80780,
    0x007FFF00,
    0x001FFC00,
    0x00000000,
    0x00000000,
    0x00000000,


    /* 0x41 'A' */
    0x00000000,
    0x00000000,
    0x0007C000,
    0x0007E000,
    0x000FE000,
    0x000FE000,
    0x000FF000,
    0x001EF000,
    0x001EF000,
    0x001EF800,
    0x003C7800,
    0x003C7C00,
    0x007C7C00,
    0x00783C00,
    0x00783E00,
    0x00F83E00,
    0x00F01E00,
    0x00FFFF00,
    0x01FFFF00,
    0x01FFFF00,
    0x03FFFF80,
    0x03C00F80,
    0x03C007C0,
    0x07C007C0,
    0x078007C0,
    0x078003E0,
    0x0F8003E0,
    0x0F0001E0,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x42 'B' */
    0x00000000,
    0x00000000,
    0x03FFF000,
    0x03FFFE00,
    0x03FFFF00,
    0x03C03F00,
    0x03C00F80,
    0x03C00F80,
    0x03C00780,
    0x03C00780,
    0x03C00F80,
    0x03C00F80,
    0x03C01F00,
    0x03FFFE00,
    0x03FFFE00,
    0x03FFFF00,
    0x03C01F80,
    0x03C007C0,
    0x03C003C0,
    0x03C003E0,
    0x03C003E0,
    0x03C003E0,
    0x03C003C0,
    0x03C007C0,
    0x03C01F80,
    0x03FFFF80,
    0x03FFFE00,
    0x03FFF800,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x43 'C' */
    0x00000000,
    0x00000000,
    0x000FFC00,
    0x003FFF00,
    0x00FFFF80,
    0x00FC07C0,
    0x01F003E0,
    0x03E001E0,
    0x03E001F0,
    0x07C000F0,
    0x07C00000,
    0x07C00000,
    0x07800000,
    0x07800000,
    0x07800000,
    0x07800000,
    0x07800000,
    0x07800000,
    0x078000F0,
    0x07C000F0,
    0x07C000F0,
    0x03C001F0,
    0x03E001E0,
    0x01F003E0,
    0x01F807C0,
    0x00FF3F80,
    0x007FFF00,
    0x001FFC00,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x44 'D' */
    0x00000000,
    0x00000000,
    0x03FFE000,
    0x03FFFC00,
    0x03FFFF00,
    0x03C03F80,
    0x03C00FC0,
    0x03C007C0,
    0x03C003E0,
    0x03C001E0,
    0x03C001F0,
    0x03C001F0,
    0x03C001F0,
    0x03C000F0,
    0x03C000F0,
    0x03C000F0,
    0x03C000F0,
    0x03C001F0,
    0x03C001F0,
    0x03C001F0,
    0x03C001E0,
    0x03C003E0,
    0x03C003C0,
    0x03C007C0,
    0x03C01F80,
    0x03FFFF00,
    0x03FFFE00,
    0x03FFF800,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x45 'E' */
    0x00000000,
    0x00000000,
    0x03FFFFC0,
    0x03FFFFC0,
    0x03FFFFC0,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03FFFF80,
    0x03FFFF80,
    0x03FFFF80,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03FFFFC0,
    0x03FFFFC0,
    0x03FFFFC0,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x46 'F' */
    0x00000000,
    0x00000000,
    0x03FFFF80,
    0x03FFFF80,
    0x03FFFF80,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03FFFE00,
    0x03FFFE00,
    0x03FFFE00,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x47 'G' */
    0x00000000,
    0x00000000,
    0x000FFE00,
    0x003FFF80,
    0x007FFFC0,
    0x00FC07E0,
    0x01F803F0,
    0x01F001F0,
    0x03E000F8,
    0x03C000F8,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07800000,
    0x07807FF8,
    0x07807FF8,
    0x07807FF8,
    0x07800078,
    0x07C00078,
    0x07C00078,
    0x07C00078,
    0x03E000F8,
    0x03E000F8,
    0x01F001F8,
    0x00F803F8,
    0x00FF1FF8,
    0x003FFF38,
    0x001FFC38,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x48 'H' */
    0x00000000,
    0x00000000,
    0x03C001E0,
    0x03C001E0,
    0x03C001E0,
    0x03C001E0,
    0x03C001E0,
    0x03C001E0,
    0x03C001E0,
    0x03C001E0,
    0x03C001E0,
    0x03C001E0,
    0x03C001E0,
    0x03FFFFE0,
    0x03FFFFE0,
    0x03FFFFE0,
    0x03C001E0,
    0x03C001E0,
    0x03C001E0,
    0x03C001E0,
    0x03C001E0,
    0x03C001E0,
    0x03C001E0,
    0x03C001E0,
    0x03C001E0,
    0x03C001E0,
    0x03C001E0,
    0x03C001E0,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x49 'I' */
    0x00000000,
    0x00000000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x4A 'J' */
    0x00000000,
    0x00000000,
    0x0001E000,
    0x0001E000,
    0x0001E000,
    0x0001E000,
    0x0001E000,
    0x0001E000,
    0x0001E000,
    0x0001E000,
    0x0001E000,
    0x0001E000,
    0x0001E000,
    0x0001E000,
    0x0001E000,
    0x0001E000,
    0x0001E000,
    0x0001E000,
    0x0001E000,
    0x0001E000,
    0x0F01E000,
    0x0F01E000,
    0x0F01E000,
    0x0F03E000,
    0x0F03E000,
    0x0FC7C000,
    0x07FF8000,
    0x01FF0000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x4B 'K' */
    0x00000000,
    0x00000000,
    0x03C007E0,
    0x03C00FC0,
    0x03C01F80,
    0x03C03F00,
    0x03C07E00,
    0x03C0FC00,
    0x03C1F800,
    0x03C3F000,
    0x03C7E000,
    0x03CFC000,
    0x03DF8000,
    0x03FF8000,
    0x03FFC000,
    0x03FFE000,
    0x03FBF000,
    0x03F1F000,
    0x03E0F800,
    0x03C0FC00,
    0x03C07C00,
    0x03C03E00,
    0x03C03F00,
    0x03C01F80,
    0x03C00F80,
    0x03C007C0,
    0x03C007E0,
    0x03C003F0,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x4C 'L' */
    0x00000000,
    0x00000000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03FFFE00,
    0x03FFFE00,
    0x03FFFE00,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x4D 'M' */
    0x00000000,
    0x00000000,
    0x03F0007E,
    0x03F0007E,
    0x03F0007E,
    0x03F800FE,
    0x03F800FE,
    0x03F800FE,
    0x03FC01FE,
    0x03FC01FE,
    0x03FC01FE,
    0x03DE03DE,
    0x03DE03DE,
    0x03DE03DE,
    0x03CF079E,
    0x03CF079E,
    0x03CF079E,
    0x03C78F1E,
    0x03C78F1E,
    0x03C78F1E,
    0x03C3DE1E,
    0x03C3DE1E,
    0x03C3FE1E,
    0x03C1FC1E,
    0x03C1FC1E,
    0x03C1FC1E,
    0x03C0F81E,
    0x03C0F81E,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x4E 'N' */
    0x00000000,
    0x00000000,
    0x03E001E0,
    0x03F001E0,
    0x03F001E0,
    0x03F801E0,
    0x03FC01E0,
    0x03FC01E0,
    0x03FE01E0,
    0x03DE01E0,
    0x03DF01E0,
    0x03CF81E0,
    0x03C781E0,
    0x03C7C1E0,
    0x03C3C1E0,
    0x03C3E1E0,
    0x03C1F1E0,
    0x03C0F1E0,
    0x03C0F9E0,
    0x03C07DE0,
    0x03C03DE0,
    0x03C03FE0,
    0x03C01FE0,
    0x03C01FE0,
    0x03C00FE0,
    0x03C007E0,
    0x03C007E0,
    0x03C003E0,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x4F 'O' */
    0x00000000,
    0x00000000,
    0x000FFE00,
    0x003FFF80,
    0x007FFFC0,
    0x00FC07E0,
    0x01F001F0,
    0x03E000F8,
    0x03E000F8,
    0x07C0007C,
    0x07C0007C,
    0x07C0003C,
    0x0780003C,
    0x0780003C,
    0x0F80003E,
    0x0F80003E,
    0x0780003C,
    0x0780003C,
    0x0780003C,
    0x07C0007C,
    0x07C0007C,
    0x03E000F8,
    0x03E000F8,
    0x01F001F0,
    0x00FC07E0,
    0x007FBFC0,
    0x003FFF80,
    0x000FFE00,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x50 'P' */
    0x00000000,
    0x00000000,
    0x03FFF800,
    0x03FFFE00,
    0x03FFFF00,
    0x03E01F80,
    0x03E007C0,
    0x03E007C0,
    0x03E003C0,
    0x03E003C0,
    0x03E003C0,
    0x03E003C0,
    0x03E007C0,
    0x03E00F80,
    0x03FFFF80,
    0x03FFFF00,
    0x03FFFC00,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x03E00000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x51 'Q' */
    0x00000000,
    0x00000000,
    0x000FFE00,
    0x003FFF80,
    0x007FFFC0,
    0x00FC07E0,
    0x01F001F0,
    0x03E000F8,
    0x03E000F8,
    0x07C0007C,
    0x07C0007C,
    0x07C0003C,
    0x0780003C,
    0x0780003C,
    0x0F80003E,
    0x0F80003E,
    0x0780003C,
    0x0780003C,
    0x0780003C,
    0x07C0007C,
    0x07C0007C,
    0x03E01CF8,
    0x03E03EF8,
    0x01F01FF0,
    0x00FC0FE0,
    0x007F9FE0,
    0x003FFFF8,
    0x000FFEFC,
    0x0000003C,
    0x00000018,
    0x00000000,
    0x00000000,

    /* 0x52 'R' */
    0x00000000,
    0x00000000,
    0x03FFFC00,
    0x03FFFF80,
    0x03FFFFC0,
    0x03E007E0,
    0x03E003E0,
    0x03E001E0,
    0x03E001F0,
    0x03E001F0,
    0x03E001F0,
    0x03E001E0,
    0x03E003E0,
    0x03E007C0,
    0x03FFFF80,
    0x03FFFF00,
    0x03FFFFC0,
    0x03E007C0,
    0x03E003E0,
    0x03E003E0,
    0x03E001E0,
    0x03E001E0,
    0x03E001E0,
    0x03E001E0,
    0x03E001E0,
    0x03E001E0,
    0x03E001E0,
    0x03E001F8,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x53 'S' */
    0x00000000,
    0x00000000,
    0x003FF800,
    0x00FFFE00,
    0x01FFFF00,
    0x01F01F00,
    0x03E00F80,
    0x03C00780,
    0x03C00780,
    0x03C007C0,
    0x03C00000,
    0x03E00000,
    0x03FC0000,
    0x01FFE000,
    0x00FFFC00,
    0x001FFF00,
    0x0001FF80,
    0x00001F80,
    0x000007C0,
    0x000003C0,
    0x078003C0,
    0x078003C0,
    0x07C003C0,
    0x07C007C0,
    0x03E00F80,
    0x01FC3F80,
    0x00FFFE00,
    0x007FFC00,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x54 'T' */
    0x00000000,
    0x00000000,
    0x0FFFFF80,
    0x0FFFFF80,
    0x0FFFFF80,
    0x000F8000,
    0x000F8000,
    0x000F8000,
    0x000F8000,
    0x000F8000,
    0x000F8000,
    0x000F8000,
    0x000F8000,
    0x000F8000,
    0x000F8000,
    0x000F8000,
    0x000F8000,
    0x000F8000,
    0x000F8000,
    0x000F8000,
    0x000F8000,
    0x000F8000,
    0x000F8000,
    0x000F8000,
    0x000F8000,
    0x000F8000,
    0x000F8000,
    0x000F8000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x55 'U' */
    0x00000000,
    0x00000000,
    0x03C001E0,
    0x03C001E0,
    0x03C001E0,
    0x03C001E0,
    0x03C001E0,
    0x03C001E0,
    0x03C001E0,
    0x03C001E0,
    0x03C001E0,
    0x03C001E0,
    0x03C001E0,
    0x03C001E0,
    0x03C001E0,
    0x03C001E0,
    0x03C001E0,
    0x03C001E0,
    0x03C001E0,
    0x03C001E0,
    0x03C003E0,
    0x03E003E0,
    0x03E003E0,
    0x01F007C0,
    0x01F80FC0,
    0x00FE7F80,
    0x007FFF00,
    0x001FFC00,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x56 'V' */
    0x00000000,
    0x00000000,
    0x0F8003E0,
    0x0F8003E0,
    0x078003E0,
    0x07C007C0,
    0x07C007C0,
    0x03C00780,
    0x03E00F80,
    0x03E00F80,
    0x01E00F00,
    0x01F01F00,
    0x01F01F00,
    0x00F01E00,
    0x00F83E00,
    0x00783C00,
    0x00783C00,
    0x007C7C00,
    0x003C7800,
    0x003C7800,
    0x003EF800,
    0x001EF000,
    0x001EF000,
    0x001FF000,
    0x000FE000,
    0x000FE000,
    0x0007C000,
    0x0007C000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x57 'W' */
    0x00000000,
    0x00000000,
    0x0F003E00,
    0x0F803E00,
    0x0F807E00,
    0x0F807F00,
    0x07807F00,
    0x07807F01,
    0x07C0F781,
    0x07C0F781,
    0x03C0F781,
    0x03C0F783,
    0x03E1E3C3,
    0x01E1E3C3,
    0x01E1E3C3,
    0x01E3C3C3,
    0x01F3C1E7,
    0x00F3C1E7,
    0x00F3C1E7,
    0x00F780F7,
    0x00FF80FF,
    0x007F80FF,
    0x007F00FF,
    0x007F007F,
    0x007F007E,
    0x003F007E,
    0x003E003E,
    0x003E003E,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x58 'X' */
    0x00000000,
    0x00000000,
    0x0FC003E0,
    0x07C007C0,
    0x03E00F80,
    0x01F00F80,
    0x01F01F00,
    0x00F83E00,
    0x007C3E00,
    0x007C7C00,
    0x003EF800,
    0x001FF000,
    0x001FF000,
    0x000FE000,
    0x0007C000,
    0x000FE000,
    0x000FE000,
    0x001FF000,
    0x003EF800,
    0x003EF800,
    0x007C7C00,
    0x00F83E00,
    0x00F83F00,
    0x01F01F00,
    0x03E00F80,
    0x07E00FC0,
    0x07C007C0,
    0x0F8003E0,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x59 'Y' */
    0x00000000,
    0x00000000,
    0x0F8003E0,
    0x0F8003E0,
    0x07C007C0,
    0x03E00F80,
    0x03E00F80,
    0x01F01F00,
    0x00F01F00,
    0x00F83E00,
    0x007C7C00,
    0x007C7C00,
    0x003EF800,
    0x001EF000,
    0x001FF000,
    0x000FE000,
    0x0007C000,
    0x0007C000,
    0x0007C000,
    0x0007C000,
    0x0007C000,
    0x0007C000,
    0x0007C000,
    0x0007C000,
    0x0007C000,
    0x0007C000,
    0x0007C000,
    0x0007C000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x5A 'Z' */
    0x00000000,
    0x00000000,
    0x07FFFF80,
    0x07FFFF80,
    0x07FFFF80,
    0x00001F80,
    0x00001F00,
    0x00003E00,
    0x00007C00,
    0x0000FC00,
    0x0001F800,
    0x0001F000,
    0x0003E000,
    0x0007C000,
    0x000FC000,
    0x001F8000,
    0x001F0000,
    0x003E0000,
    0x007C0000,
    0x00FC0000,
    0x00F80000,
    0x01F00000,
    0x03E00000,
    0x07C00000,
    0x0FC00000,
    0x0FFFFF80,
    0x0FFFFF80,
    0x0FFFFF80,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x5B '[' */
    0x00000000,
    0x00000000,
    0x07F80000,
    0x07F80000,
    0x07F80000,
    0x07800000,
    0x07800000,
    0x07800000,
    0x07800000,
    0x07800000,
    0x07800000,
    0x07800000,
    0x07800000,
    0x07800000,
    0x07800000,
    0x07800000,
    0x07800000,
    0x07800000,
    0x07800000,
    0x07800000,
    0x07800000,
    0x07800000,
    0x07800000,
    0x07800000,
    0x07800000,
    0x07800000,
    0x07800000,
    0x07800000,
    0x07800000,
    0x07800000,
    0x07800000,
    0x07800000,

    /* 0x5C '\' */
    0x00000000,
    0x00000000,
    0x1C000000,
    0x1C000000,
    0x0E000000,
    0x0E000000,
    0x0E000000,
    0x07000000,
    0x07000000,
    0x07000000,
    0x03800000,
    0x03800000,
    0x03800000,
    0x03C00000,
    0x01C00000,
    0x01C00000,
    0x01E00000,
    0x00E00000,
    0x00E00000,
    0x00F00000,
    0x00700000,
    0x00700000,
    0x00780000,
    0x00380000,
    0x00380000,
    0x003C0000,
    0x001C0000,
    0x001C0000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x5D ']' */
    0x00000000,
    0x00000000,
    0x0FF00000,
    0x0FF00000,
    0x0FF00000,
    0x00F00000,
    0x00F00000,
    0x00F00000,
    0x00F00000,
    0x00F00000,
    0x00F00000,
    0x00F00000,
    0x00F00000,
    0x00F00000,
    0x00F00000,
    0x00F00000,
    0x00F00000,
    0x00F00000,
    0x00F00000,
    0x00F00000,
    0x00F00000,
    0x00F00000,
    0x00F00000,
    0x00F00000,
    0x00F00000,
    0x00F00000,
    0x00F00000,
    0x00F00000,
    0x00F00000,
    0x00F00000,
    0x00F00000,
    0x00F00000,

    /* 0x5E '^' */
    0x00000000,
    0x00000000,
    0x01F00000,
    0x03F80000,
    0x07BC0000,
    0x073C0000,
    0x0F1E0000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x5F '_' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x1FFFFF00,
    0x1FFFFF00,

    /* 0x60 '`' */
    0x00000000,
    0x00000000,
    0x0F800000,
    0x07C00000,
    0x03C00000,
    0x01E00000,
    0x00E00000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x61 'a' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00FFC000,
    0x01FFE000,
    0x03F3F000,
    0x03C0F800,
    0x07C07800,
    0x07807800,
    0x00007800,
    0x0000F800,
    0x00FFF800,
    0x03FFF800,
    0x07FC7800,
    0x07C07800,
    0x0F807800,
    0x0F807800,
    0x0F80F800,
    0x0F81F800,
    0x07C7F800,
    0x03FFFE00,
    0x01FE3E00,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x62 'b' */
    0x00000000,
    0x00000000,
    0x07800000,
    0x07800000,
    0x07800000,
    0x07800000,
    0x07800000,
    0x07800000,
    0x07800000,
    0x07BFC000,
    0x07FFF000,
    0x07FFF800,
    0x07E0F800,
    0x07C07C00,
    0x07C03C00,
    0x07C03C00,
    0x07803C00,
    0x07803E00,
    0x07803E00,
    0x07803C00,
    0x07803C00,
    0x07803C00,
    0x07C03C00,
    0x07C07800,
    0x07E07800,
    0x07F1F000,
    0x07FFE000,
    0x07BFC000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x63 'c' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x007FC000,
    0x01FFE000,
    0x03FFF000,
    0x03E0F000,
    0x07C0F800,
    0x07807800,
    0x07807800,
    0x0F800000,
    0x0F000000,
    0x0F000000,
    0x0F000000,
    0x0F000000,
    0x0F807800,
    0x07807800,
    0x0780F800,
    0x07C0F000,
    0x03E3F000,
    0x01FFE000,
    0x007F8000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x64 'd' */
    0x00000000,
    0x00000000,
    0x00003C00,
    0x00003C00,
    0x00003C00,
    0x00003C00,
    0x00003C00,
    0x00003C00,
    0x00003C00,
    0x007F3C00,
    0x01FFBC00,
    0x03FFFC00,
    0x03E1FC00,
    0x07C0FC00,
    0x07807C00,
    0x07807C00,
    0x0F807C00,
    0x0F803C00,
    0x0F803C00,
    0x0F803C00,
    0x0F803C00,
    0x07803C00,
    0x07807C00,
    0x07C07C00,
    0x03C0FC00,
    0x03F3FC00,
    0x01FFFC00,
    0x007FBC00,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x65 'e' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x007FC000,
    0x00FFE000,
    0x01FFF000,
    0x03E0F800,
    0x07C07800,
    0x07803C00,
    0x07803C00,
    0x07803C00,
    0x07FFFC00,
    0x0FFFFE00,
    0x0FFFFE00,
    0x07800000,
    0x07800000,
    0x07803C00,
    0x07C03C00,
    0x03C07800,
    0x03F1F800,
    0x01FFF000,
    0x007FE000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x66 'f' */
    0x00000000,
    0x00000000,
    0x00F80000,
    0x01F80000,
    0x03F80000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x0FF80000,
    0x0FF80000,
    0x0FF80000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x67 'g' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x007FBC00,
    0x01FFFC00,
    0x03FFFC00,
    0x03E0FC00,
    0x07C07C00,
    0x07807C00,
    0x07807C00,
    0x07803C00,
    0x0F803C00,
    0x0F803C00,
    0x0F803C00,
    0x07803C00,
    0x07803C00,
    0x07807C00,
    0x07C07C00,
    0x03E0FC00,
    0x01F1FC00,
    0x00FFFC00,
    0x007FBC00,
    0x00003C00,
    0x00003C00,
    0x07807800,
    0x07C07800,

    /* 0x68 'h' */
    0x00000000,
    0x00000000,
    0x07800000,
    0x07800000,
    0x07800000,
    0x07800000,
    0x07800000,
    0x07800000,
    0x07800000,
    0x079FE000,
    0x07BFF000,
    0x07FFF800,
    0x07F07800,
    0x07E07800,
    0x07C07C00,
    0x07C07C00,
    0x07C07C00,
    0x07807C00,
    0x07807C00,
    0x07807C00,
    0x07807C00,
    0x07807C00,
    0x07807C00,
    0x07807C00,
    0x07807C00,
    0x07807C00,
    0x07807C00,
    0x07807C00,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x69 'i' */
    0x00000000,
    0x00000000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x6A 'j' */
    0x00000000,
    0x00000000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,

    /* 0x6B 'k' */
    0x00000000,
    0x00000000,
    0x07800000,
    0x07800000,
    0x07800000,
    0x07800000,
    0x07800000,
    0x07800000,
    0x07800000,
    0x0780F800,
    0x0781F000,
    0x0783E000,
    0x0787C000,
    0x078F8000,
    0x079F0000,
    0x07BE0000,
    0x07FE0000,
    0x07FE0000,
    0x07FF0000,
    0x07EF8000,
    0x07CF8000,
    0x0787C000,
    0x0783C000,
    0x0783E000,
    0x0781F000,
    0x0781F000,
    0x0780F800,
    0x07807C00,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x6C 'l' */
    0x00000000,
    0x00000000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x07C00000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x6D 'm' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x079FC7F8,
    0x07FFEFFC,
    0x07FFFFFE,
    0x07F0FC3E,
    0x07E0F81F,
    0x07C0F81F,
    0x07C0F81F,
    0x07C0F81F,
    0x0780F81F,
    0x0780F81F,
    0x0780F81F,
    0x0780F81F,
    0x0780F81F,
    0x0780F81F,
    0x0780F81F,
    0x0780F81F,
    0x0780F81F,
    0x0780F81F,
    0x0780F81F,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x6E 'n' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x079FE000,
    0x07FFF000,
    0x07FFF800,
    0x07F0F800,
    0x07E07800,
    0x07C07C00,
    0x07C07C00,
    0x07C07C00,
    0x07807C00,
    0x07807C00,
    0x07807C00,
    0x07807C00,
    0x07807C00,
    0x07807C00,
    0x07807C00,
    0x07807C00,
    0x07807C00,
    0x07807C00,
    0x07807C00,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x6F 'o' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x007FC000,
    0x00FFE000,
    0x01FBF000,
    0x03E0F800,
    0x07C07C00,
    0x07803C00,
    0x07803C00,
    0x0F803C00,
    0x0F803E00,
    0x0F803E00,
    0x0F803E00,
    0x0F803C00,
    0x07803C00,
    0x07803C00,
    0x07C07C00,
    0x03E0F800,
    0x01F1F000,
    0x00FFE000,
    0x007FC000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x70 'p' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x07BFC000,
    0x07FFF000,
    0x07FFF800,
    0x07E0F800,
    0x07C07C00,
    0x07C03C00,
    0x07C03C00,
    0x07803C00,
    0x07803E00,
    0x07803E00,
    0x07803C00,
    0x07803C00,
    0x07803C00,
    0x07C03C00,
    0x07C07800,
    0x07E07800,
    0x07F1F000,
    0x07FFE000,
    0x07BFC000,
    0x07800000,
    0x07800000,
    0x07800000,
    0x07800000,

    /* 0x71 'q' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x007F3C00,
    0x01FFBC00,
    0x03FFFC00,
    0x03E1FC00,
    0x07C0FC00,
    0x07807C00,
    0x07807C00,
    0x0F807C00,
    0x0F803C00,
    0x0F803C00,
    0x0F803C00,
    0x0F803C00,
    0x07803C00,
    0x07807C00,
    0x07C07C00,
    0x03C0FC00,
    0x03F3FC00,
    0x01FFFC00,
    0x007FBC00,
    0x00003C00,
    0x00003C00,
    0x00003C00,
    0x00003C00,

    /* 0x72 'r' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x03DF0000,
    0x03FF0000,
    0x03FF0000,
    0x03F80000,
    0x03E00000,
    0x03E00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x73 's' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00FF8000,
    0x03FFC000,
    0x03F7E000,
    0x0781F000,
    0x0780F000,
    0x0780F000,
    0x07800000,
    0x07F00000,
    0x03FF0000,
    0x01FFC000,
    0x003FF000,
    0x0003F000,
    0x0000F800,
    0x0F00F800,
    0x0F00F000,
    0x0780F000,
    0x07E3F000,
    0x03FFE000,
    0x00FF8000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x74 't' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x0FF80000,
    0x0FF80000,
    0x0FF80000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03E00000,
    0x03F80000,
    0x01F80000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x75 'u' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x07C07C00,
    0x07C07C00,
    0x07C07C00,
    0x07C07C00,
    0x07C07C00,
    0x07C07C00,
    0x07C07C00,
    0x07C07C00,
    0x07C07C00,
    0x07C07C00,
    0x07C07C00,
    0x07C07C00,
    0x07C07C00,
    0x07C07C00,
    0x07C0FC00,
    0x03C0FC00,
    0x03E3FC00,
    0x01FFBC00,
    0x00FF3C00,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x76 'v' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x1F007800,
    0x0F007800,
    0x0F80F800,
    0x0780F000,
    0x0780F000,
    0x07C1F000,
    0x03C1E000,
    0x03C1E000,
    0x03E3C000,
    0x01E3C000,
    0x01E3C000,
    0x01E78000,
    0x00F78000,
    0x00F78000,
    0x007F0000,
    0x007F0000,
    0x007E0000,
    0x003E0000,
    0x003E0000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x77 'w' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x0F03E078,
    0x0F03E078,
    0x0F03E078,
    0x0787F0F0,
    0x0787F0F0,
    0x0787F0F0,
    0x0787F0F0,
    0x03CF79E0,
    0x03CF79E0,
    0x03CF79E0,
    0x03CE39C0,
    0x01FE3FC0,
    0x01FE3FC0,
    0x01FE3FC0,
    0x00FC1F80,
    0x00FC1F80,
    0x00FC1F80,
    0x00FC1F00,
    0x00780F00,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x78 'x' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x0F80F800,
    0x07C0F000,
    0x07C1F000,
    0x03E3E000,
    0x01F3C000,
    0x01F7C000,
    0x00FF8000,
    0x007F0000,
    0x007F0000,
    0x003E0000,
    0x007F0000,
    0x007F0000,
    0x00FF8000,
    0x01F7C000,
    0x03E3E000,
    0x03E3E000,
    0x07C1F000,
    0x0F80F800,
    0x0F80F800,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x79 'y' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x0F007800,
    0x0F007800,
    0x0F80F800,
    0x0780F000,
    0x0780F000,
    0x07C1F000,
    0x03C1E000,
    0x03C1E000,
    0x03E3E000,
    0x01E3C000,
    0x01E3C000,
    0x00F78000,
    0x00F78000,
    0x00F78000,
    0x007F0000,
    0x007F0000,
    0x007F0000,
    0x003E0000,
    0x003E0000,
    0x003C0000,
    0x003C0000,
    0x00780000,
    0x00780000,

    /* 0x7A 'z' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x07FFF000,
    0x07FFF000,
    0x07FFF000,
    0x0001F000,
    0x0003E000,
    0x0007C000,
    0x000F8000,
    0x000F8000,
    0x001F0000,
    0x003E0000,
    0x007C0000,
    0x00F80000,
    0x01F80000,
    0x01F00000,
    0x03E00000,
    0x07C00000,
    0x0FFFF800,
    0x0FFFF800,
    0x0FFFF800,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x7B '{' */
    0x00000000,
    0x00000000,
    0x003E0000,
    0x00FE0000,
    0x00F00000,
    0x01E00000,
    0x01E00000,
    0x01E00000,
    0x01E00000,
    0x01E00000,
    0x01E00000,
    0x01E00000,
    0x01E00000,
    0x01E00000,
    0x01E00000,
    0x01E00000,
    0x03C00000,
    0x07C00000,
    0x07000000,
    0x07800000,
    0x03C00000,
    0x01E00000,
    0x01E00000,
    0x01E00000,
    0x01E00000,
    0x01E00000,
    0x01E00000,
    0x01E00000,
    0x01E00000,
    0x01E00000,
    0x01E00000,
    0x00F00000,

    /* 0x7C '|' */
    0x00000000,
    0x00000000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x03C00000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x7D '}' */
    0x00000000,
    0x00000000,
    0x07800000,
    0x07E00000,
    0x01E00000,
    0x01E00000,
    0x00F00000,
    0x00F00000,
    0x00F00000,
    0x00F00000,
    0x00F00000,
    0x00F00000,
    0x00F00000,
    0x00F00000,
    0x00F00000,
    0x00F00000,
    0x00780000,
    0x007E0000,
    0x001E0000,
    0x007E0000,
    0x00780000,
    0x00F00000,
    0x00F00000,
    0x00F00000,
    0x00F00000,
    0x00F00000,
    0x00F00000,
    0x00F00000,
    0x00F00000,
    0x00F00000,
    0x00F00000,
    0x01E00000,

    /* 0x7E '~' */
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00600C00,
    0x01FC0C00,
    0x03FF9E00,
    0x078FFC00,
    0x0303F800,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,

    /* 0x7F '' */
    0x00000000,
    0x00000000,
    0x0FFC0000,
    0x0FFC0000,
    0x0E1C0000,
    0x0E1C0000,
    0x0E1C0000,
    0x0E1C0000,
    0x0E1C0000,
    0x0E1C0000,
    0x0E1C0000,
    0x0E1C0000,
    0x0E1C0000,
    0x0E1C0000,
    0x0E1C0000,
    0x0E1C0000,
    0x0E1C0000,
    0x0E1C0000,
    0x0E1C0000,
    0x0E1C0000,
    0x0E1C0000,
    0x0E1C0000,
    0x0E1C0000,
    0x0E1C0000,
    0x0E1C0000,
    0x0E1C0000,
    0x0E1C0000,
    0x0FFC0000,
    0x0FFC0000,
    0x00000000,
    0x00000000,
    0x00000000,
#endif /* AIC_BOOTLOADER_CMD_PROGRESS_BAR_ROTATE */
};
#endif /* AIC_BOOTLOADER_CMD_FB_CONSOLE */

#endif /* _VIDEO_FONT_DATA_ */

