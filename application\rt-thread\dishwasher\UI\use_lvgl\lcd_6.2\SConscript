Import('RTT_ROOT')
Import('rtconfig')
from building import *
import os

cwd     = GetCurrentDir()
src     = Glob('*.c')
CPPPATH = [cwd, ]

CFLAGS = ' -c -ffunction-sections'
    
group = DefineGroup('lcd_6.2', src, depend = [''], CPPPATH = CPPPATH, CFLAGS=CFLAGS)

# 根据不同的样式配置加载对应的资源
if GetDepend(['STYLE1']):
    group = group + SConscript(os.path.join('style1', 'SConscript'))
elif GetDepend(['STYLE2']):
    group = group + SConscript(os.path.join('style2', 'SConscript'))

Return('group')
