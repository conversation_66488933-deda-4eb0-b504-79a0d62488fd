/*
 * Copyright (c) 2024, ArtInChip Technology Co., Ltd
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * QR Code Data Manager - MTD-based implementation
 * Authors: <AUTHORS>
 */

#include "qrcode_data_manager.h"
#include <string.h>
#include <stdio.h>
#include <stdlib.h>

#include <rtthread.h>

#ifdef KERNEL_BAREMETAL
#include <mtd.h>
#include <aic_crc32.h>
#else
#include <rtdevice.h>
#include <fal.h>
#endif


/* Internal data structure */
static struct {
    bool initialized;
    char *data_buffer;
    struct qrcode_data_header header;
    rt_mutex_t mutex;
} qrcode_manager = {0};

/* Helper functions */
static uint32_t calculate_crc32(const void *data, size_t len)
{
#ifdef KERNEL_BAREMETAL
    return aic_crc32(0, (const uint8_t *)data, len);
#else
    /* Simple CRC32 implementation for RT-Thread */
    uint32_t crc = 0xFFFFFFFF;
    const uint8_t *ptr = (const uint8_t *)data;
    
    for (size_t i = 0; i < len; i++) {
        crc ^= ptr[i];
        for (int j = 0; j < 8; j++) {
            if (crc & 1) {
                crc = (crc >> 1) ^ 0xEDB88320;
            } else {
                crc >>= 1;
            }
        }
    }
    return ~crc;
#endif
}

static rt_err_t qrcode_flash_read(void *buffer, size_t size)
{
#ifdef KERNEL_BAREMETAL
    struct mtd_dev *mtd = mtd_get_device(QRCODE_MTD_DEVICE_NAME);
    if (!mtd) {
        QRCODE_DEBUG("Failed to get MTD device: %s", QRCODE_MTD_DEVICE_NAME);
        return -RT_ERROR;
    }
    
    if (mtd_read(mtd, QRCODE_DATA_OFFSET, (uint8_t *)buffer, size) < 0) {
        QRCODE_DEBUG("MTD read failed");
        return -RT_ERROR;
    }
#else
    /* Use FAL for RT-Thread */
    const struct fal_partition *part = fal_partition_find("qrcode_data");
    if (!part) {
        QRCODE_DEBUG("Failed to find qrcode_data partition");
        return -RT_ERROR;
    }
    
    if (fal_partition_read(part, 0, (uint8_t *)buffer, size) < 0) {
        QRCODE_DEBUG("FAL read failed");
        return -RT_ERROR;
    }
#endif
    return RT_EOK;
}

static rt_err_t qrcode_flash_write(const void *buffer, size_t size)
{
#ifdef KERNEL_BAREMETAL
    struct mtd_dev *mtd = mtd_get_device(QRCODE_MTD_DEVICE_NAME);
    if (!mtd) {
        QRCODE_DEBUG("Failed to get MTD device: %s", QRCODE_MTD_DEVICE_NAME);
        return -RT_ERROR;
    }
    
    /* Erase before write */
    if (mtd_erase(mtd, QRCODE_DATA_OFFSET, QRCODE_DATA_SIZE) < 0) {
        QRCODE_DEBUG("MTD erase failed");
        return -RT_ERROR;
    }
    
    if (mtd_write(mtd, QRCODE_DATA_OFFSET, (uint8_t *)buffer, size) < 0) {
        QRCODE_DEBUG("MTD write failed");
        return -RT_ERROR;
    }
#else
    /* Use FAL for RT-Thread */
    const struct fal_partition *part = fal_partition_find("qrcode_data");
    if (!part) {
        QRCODE_DEBUG("Failed to find qrcode_data partition");
        return -RT_ERROR;
    }
    
    /* Erase before write */
    if (fal_partition_erase(part, 0, QRCODE_DATA_SIZE) < 0) {
        QRCODE_DEBUG("FAL erase failed");
        return -RT_ERROR;
    }
    
    if (fal_partition_write(part, 0, (uint8_t *)buffer, size) < 0) {
        QRCODE_DEBUG("FAL write failed");
        return -RT_ERROR;
    }
#endif
    return RT_EOK;
}

static rt_err_t qrcode_load_data(void)
{
    rt_err_t ret;
    uint32_t calc_crc;
    uint8_t *read_buffer;
    size_t total_size;
    
    /* Calculate total size needed */
    total_size = sizeof(struct qrcode_data_header) + QRCODE_DATA_SIZE - sizeof(struct qrcode_data_header);
    read_buffer = rt_malloc(total_size);
    if (!read_buffer) {
        QRCODE_DEBUG("Failed to allocate read buffer");
        return -RT_ENOMEM;
    }
    
    /* Read entire data block (header + data) */
    ret = qrcode_flash_read(read_buffer, total_size);
    if (ret != RT_EOK) {
        rt_free(read_buffer);
        return ret;
    }
    
    /* Copy header */
    memcpy(&qrcode_manager.header, read_buffer, sizeof(struct qrcode_data_header));
    
    /* Check magic number */
    if (qrcode_manager.header.magic != QRCODE_DATA_MAGIC) {
        QRCODE_DEBUG("Invalid magic number, initializing with defaults");
        memset(&qrcode_manager.header, 0, sizeof(struct qrcode_data_header));
        qrcode_manager.header.magic = QRCODE_DATA_MAGIC;
        memset(qrcode_manager.data_buffer, 0, QRCODE_DATA_SIZE - sizeof(struct qrcode_data_header));
        rt_free(read_buffer);
        return RT_EOK;
    }
    
    /* Copy data if exists and valid */
    if (qrcode_manager.header.data_len > 0) {
        if (qrcode_manager.header.data_len > (QRCODE_DATA_SIZE - sizeof(struct qrcode_data_header))) {
            QRCODE_DEBUG("Invalid data_len from flash: %d", qrcode_manager.header.data_len);
            /* Handle error, maybe reset to default */
            memset(&qrcode_manager.header, 0, sizeof(struct qrcode_data_header));
            qrcode_manager.header.magic = QRCODE_DATA_MAGIC;
            memset(qrcode_manager.data_buffer, 0, QRCODE_DATA_SIZE - sizeof(struct qrcode_data_header));
        } else {
            memcpy(qrcode_manager.data_buffer, read_buffer + sizeof(struct qrcode_data_header), qrcode_manager.header.data_len);
        
            /* Verify CRC */
            calc_crc = calculate_crc32(qrcode_manager.data_buffer, qrcode_manager.header.data_len);
        
        /* Debug: Print CRC verification details */
        QRCODE_DEBUG("Load: data_len=%d, stored_crc=0x%08x, calculated_crc=0x%08x", 
                     qrcode_manager.header.data_len, qrcode_manager.header.crc32, calc_crc);
        
        /* Debug: Dump first 32 bytes of data being loaded */
        // QRCODE_DEBUG("Load data dump (first 32 bytes):");
        // int dump_len = (32 < qrcode_manager.header.data_len) ? 32 : qrcode_manager.header.data_len;
        // for (int i = 0; i < dump_len; i += 16) {
        //     char hex_str[64] = {0};
        //     char ascii_str[17] = {0};
        //     for (int j = 0; j < 16 && (i + j) < qrcode_manager.header.data_len; j++) {
        //         sprintf(hex_str + j * 3, "%02x ", qrcode_manager.data_buffer[i + j]);
        //         ascii_str[j] = (qrcode_manager.data_buffer[i + j] >= 32 && qrcode_manager.data_buffer[i + j] < 127) ? 
        //                       qrcode_manager.data_buffer[i + j] : '.';
        //     }
        //     QRCODE_DEBUG("%04x: %s %s", i, hex_str, ascii_str);
        // }
        
            if (calc_crc != qrcode_manager.header.crc32) {
                QRCODE_DEBUG("CRC mismatch, data corrupted");
                memset(&qrcode_manager.header, 0, sizeof(struct qrcode_data_header));
                qrcode_manager.header.magic = QRCODE_DATA_MAGIC;
                memset(qrcode_manager.data_buffer, 0, QRCODE_DATA_SIZE - sizeof(struct qrcode_data_header));
            } else {
                QRCODE_DEBUG("CRC verification passed");
            }
        }
    }
    
    rt_free(read_buffer);
    return RT_EOK;
}

static rt_err_t qrcode_save_data(void)
{
    rt_err_t ret;
    uint8_t *write_buffer;
    size_t total_size;
    
    /* Calculate CRC */
    qrcode_manager.header.crc32 = calculate_crc32(qrcode_manager.data_buffer, qrcode_manager.header.data_len);
    
    /* Debug: Print CRC calculation details */
    QRCODE_DEBUG("Save: data_len=%d, calculated_crc=0x%08x", qrcode_manager.header.data_len, qrcode_manager.header.crc32);
    
    /* Debug: Dump first 32 bytes of data being saved */
    QRCODE_DEBUG("Save data dump (first 32 bytes):");
    int dump_len = (32 < qrcode_manager.header.data_len) ? 32 : qrcode_manager.header.data_len;
    for (int i = 0; i < dump_len; i += 16) {
        char hex_str[64] = {0};
        char ascii_str[17] = {0};
        for (int j = 0; j < 16 && (i + j) < qrcode_manager.header.data_len; j++) {
            sprintf(hex_str + j * 3, "%02x ", qrcode_manager.data_buffer[i + j]);
            ascii_str[j] = (qrcode_manager.data_buffer[i + j] >= 32 && qrcode_manager.data_buffer[i + j] < 127) ? 
                          qrcode_manager.data_buffer[i + j] : '.';
        }
        QRCODE_DEBUG("%04x: %s %s", i, hex_str, ascii_str);
    }
    
    /* Prepare write buffer */
    total_size = sizeof(struct qrcode_data_header) + qrcode_manager.header.data_len;
    write_buffer = rt_malloc(total_size);
    if (!write_buffer) {
        QRCODE_DEBUG("Failed to allocate write buffer");
        return -RT_ENOMEM;
    }
    
    /* Copy header and data */
    memcpy(write_buffer, &qrcode_manager.header, sizeof(struct qrcode_data_header));
    if (qrcode_manager.header.data_len > 0) {
        memcpy(write_buffer + sizeof(struct qrcode_data_header), 
               qrcode_manager.data_buffer, qrcode_manager.header.data_len);
    }
    
    /* Write to flash */
    ret = qrcode_flash_write(write_buffer, total_size);
    
    rt_free(write_buffer);
    return ret;
}

static char *find_key_value(const char *key)
{
    char *ptr = qrcode_manager.data_buffer;
    char *end = ptr + qrcode_manager.header.data_len;
    
    while (ptr < end && *ptr) {
        char *eq_pos = strchr(ptr, '=');
        if (eq_pos) {
            size_t key_len = eq_pos - ptr;
            if (strlen(key) == key_len && strncmp(ptr, key, key_len) == 0) {
                return eq_pos + 1;
            }
        }
        
        /* Move to next entry */
        ptr += strlen(ptr) + 1;
    }
    
    return NULL;
}

static rt_err_t set_key_value(const char *key, const char *value)
{
    char *old_value = find_key_value(key);
    char *new_data;
    size_t new_len;
    size_t key_len = strlen(key);
    size_t value_len = value ? strlen(value) : 0;
    
    if (old_value) {
        /* Key exists, replace value */
        char *old_entry_start = old_value - key_len - 1;
        char *old_entry_end = old_value + strlen(old_value) + 1;
        size_t old_entry_len = old_entry_end - old_entry_start;
        
        if (!value) {
            /* Delete entry */
            memmove(old_entry_start, old_entry_end, 
                   qrcode_manager.data_buffer + qrcode_manager.header.data_len - old_entry_end);
            qrcode_manager.header.data_len -= old_entry_len;
        } else {
            /* Replace entry */
            size_t new_entry_len = key_len + 1 + value_len + 1;
            size_t remaining_len = qrcode_manager.data_buffer + qrcode_manager.header.data_len - old_entry_end;
            
            if (new_entry_len > old_entry_len) {
                if (qrcode_manager.header.data_len + (new_entry_len - old_entry_len) >= QRCODE_DATA_SIZE - sizeof(struct qrcode_data_header)) {
                    QRCODE_DEBUG("Data buffer full on replace");
                    return -RT_EFULL;
                }
            }

            if (new_entry_len != old_entry_len) {
                memmove(old_entry_start + new_entry_len, old_entry_end, remaining_len);
                qrcode_manager.header.data_len += (new_entry_len - old_entry_len);
            }
            
            sprintf(old_entry_start, "%s=%s", key, value);
            old_entry_start[new_entry_len - 1] = '\0';
        }
    } else if (value) {
        /* Add new entry */
        size_t new_entry_len = key_len + 1 + value_len + 1;
        
        if (qrcode_manager.header.data_len + new_entry_len >= QRCODE_DATA_SIZE - sizeof(struct qrcode_data_header)) {
            QRCODE_DEBUG("Data buffer full");
            return -RT_EFULL;
        }
        
        sprintf(qrcode_manager.data_buffer + qrcode_manager.header.data_len, "%s=%s", key, value);
        qrcode_manager.header.data_len += new_entry_len;
    }
    
    return RT_EOK;
}

/* Public API implementation */

rt_err_t qrcode_data_manager_init(void)
{
    rt_err_t ret;
    
    if (qrcode_manager.initialized) {
        return RT_EOK;
    }
    
    /* Create mutex */
    qrcode_manager.mutex = rt_mutex_create("qrcode_mtx", RT_IPC_FLAG_PRIO);
    if (!qrcode_manager.mutex) {
        QRCODE_DEBUG("Failed to create mutex");
        return -RT_ERROR;
    }
    
    /* Allocate data buffer */
    qrcode_manager.data_buffer = rt_malloc(QRCODE_DATA_SIZE - sizeof(struct qrcode_data_header));
    if (!qrcode_manager.data_buffer) {
        QRCODE_DEBUG("Failed to allocate data buffer");
        rt_mutex_delete(qrcode_manager.mutex);
        return -RT_ENOMEM;
    }
    
    /* Load existing data */
    ret = qrcode_load_data();
    if (ret != RT_EOK) {
        QRCODE_DEBUG("Failed to load data, cleaning up.");
        rt_free(qrcode_manager.data_buffer);
        qrcode_manager.data_buffer = NULL;
        rt_mutex_delete(qrcode_manager.mutex);
        return ret;
    }
    
    qrcode_manager.initialized = true;
    QRCODE_DEBUG("QR code data manager initialized");
    
    return RT_EOK;
}

rt_err_t qrcode_data_manager_deinit(void)
{
    if (!qrcode_manager.initialized) {
        return RT_EOK;
    }
    
    rt_mutex_take(qrcode_manager.mutex, RT_WAITING_FOREVER);
    
    if (qrcode_manager.data_buffer) {
        rt_free(qrcode_manager.data_buffer);
        qrcode_manager.data_buffer = NULL;
    }
    
    rt_mutex_release(qrcode_manager.mutex);
    rt_mutex_delete(qrcode_manager.mutex);
    
    qrcode_manager.initialized = false;
    QRCODE_DEBUG("QR code data manager deinitialized");
    
    return RT_EOK;
}

rt_err_t qrcode_data_save_string(const char *key, const char *value)
{
    rt_err_t ret;
    
    if (!qrcode_manager.initialized) {
        return -RT_ERROR;
    }
    
    if (!key) {
        return -RT_EINVAL;
    }
    
    rt_mutex_take(qrcode_manager.mutex, RT_WAITING_FOREVER);
    
    ret = set_key_value(key, value);
    if (ret == RT_EOK) {
        ret = qrcode_save_data();
    }
    
    rt_mutex_release(qrcode_manager.mutex);
    
    QRCODE_DEBUG("Save string: %s=%s, result=%d", key, value ? value : "(null)", ret);
    return ret;
}

rt_err_t qrcode_data_get_string(const char *key, char *buffer, size_t buffer_size)
{
    char *value;
    
    if (!qrcode_manager.initialized || !key || !buffer || buffer_size == 0) {
        return -RT_EINVAL;
    }
    
    rt_mutex_take(qrcode_manager.mutex, RT_WAITING_FOREVER);
    
    value = find_key_value(key);
    if (value) {
        strncpy(buffer, value, buffer_size - 1);
        buffer[buffer_size - 1] = '\0';
        rt_mutex_release(qrcode_manager.mutex);
        return RT_EOK;
    }
    
    rt_mutex_release(qrcode_manager.mutex);
    return -RT_EEMPTY;
}

rt_err_t qrcode_data_delete_string(const char *key)
{
    return qrcode_data_save_string(key, NULL);
}

rt_err_t qrcode_data_save_device_info(const char *device_id, const char *device_sn, 
                                      const char *model, const char *version)
{
    rt_err_t ret = RT_EOK;
    
    if (device_id) {
        ret = qrcode_data_save_string(QRCODE_KEY_DEVICE_ID, device_id);
        if (ret != RT_EOK) return ret;
    }
    
    if (device_sn) {
        ret = qrcode_data_save_string(QRCODE_KEY_DEVICE_SN, device_sn);
        if (ret != RT_EOK) return ret;
    }
    
    if (model) {
        ret = qrcode_data_save_string("model", model);
        if (ret != RT_EOK) return ret;
    }
    
    if (version) {
        ret = qrcode_data_save_string("version", version);
    }
    
    return ret;
}

rt_err_t qrcode_data_save_wifi_info(const char *ssid, const char *password)
{
    rt_err_t ret = RT_EOK;
    
    if (ssid) {
        ret = qrcode_data_save_string(QRCODE_KEY_WIFI_SSID, ssid);
        if (ret != RT_EOK) return ret;
    }
    
    if (password) {
        ret = qrcode_data_save_string(QRCODE_KEY_WIFI_PASSWORD, password);
    }
    
    return ret;
}

static rt_err_t qrcode_data_get_string_internal(const char *key, char *buffer, size_t buffer_size)
{
    char *value = find_key_value(key);
    if (value) {
        strncpy(buffer, value, buffer_size - 1);
        buffer[buffer_size - 1] = '\0';
        return RT_EOK;
    }
    return -RT_EEMPTY;
}

rt_err_t qrcode_data_generate_qr_string(char *buffer, size_t buffer_size)
{
    char temp_buffer[QRCODE_MAX_STRING_LEN];
    int pos = 0;
    int n;

    if (!qrcode_manager.initialized || !buffer || buffer_size == 0) {
        return -RT_EINVAL;
    }

    rt_mutex_take(qrcode_manager.mutex, RT_WAITING_FOREVER);

    /* Start JSON */
    n = snprintf(buffer + pos, buffer_size - pos, "{");
    if (n >= buffer_size - pos) goto error_exit;
    pos += n;

    /* Add device info */
    if (qrcode_data_get_string_internal(QRCODE_KEY_DEVICE_ID, temp_buffer, sizeof(temp_buffer)) == RT_EOK) {
        n = snprintf(buffer + pos, buffer_size - pos, "\"device_id\":\"%s\",", temp_buffer);
        if (n >= buffer_size - pos) goto error_exit;
        pos += n;
    }

    if (qrcode_data_get_string_internal(QRCODE_KEY_DEVICE_SN, temp_buffer, sizeof(temp_buffer)) == RT_EOK) {
        n = snprintf(buffer + pos, buffer_size - pos, "\"device_sn\":\"%s\",", temp_buffer);
        if (n >= buffer_size - pos) goto error_exit;
        pos += n;
    }

    /* Add WiFi info */
    if (qrcode_data_get_string_internal(QRCODE_KEY_WIFI_SSID, temp_buffer, sizeof(temp_buffer)) == RT_EOK) {
        n = snprintf(buffer + pos, buffer_size - pos, "\"wifi_ssid\":\"%s\",", temp_buffer);
        if (n >= buffer_size - pos) goto error_exit;
        pos += n;
    }

    if (qrcode_data_get_string_internal(QRCODE_KEY_WIFI_PASSWORD, temp_buffer, sizeof(temp_buffer)) == RT_EOK) {
        n = snprintf(buffer + pos, buffer_size - pos, "\"wifi_password\":\"%s\",", temp_buffer);
        if (n >= buffer_size - pos) goto error_exit;
        pos += n;
    }

    /* Add other info */
    if (qrcode_data_get_string_internal(QRCODE_KEY_SERVER_URL, temp_buffer, sizeof(temp_buffer)) == RT_EOK) {
        n = snprintf(buffer + pos, buffer_size - pos, "\"server_url\":\"%s\",", temp_buffer);
        if (n >= buffer_size - pos) goto error_exit;
        pos += n;
    }

    /* Remove trailing comma */
    if (pos > 1 && buffer[pos - 1] == ',') {
        pos--;
    }

    /* End JSON */
    n = snprintf(buffer + pos, buffer_size - pos, "}");
    if (n >= buffer_size - pos) goto error_exit;
    pos += n;

    rt_mutex_release(qrcode_manager.mutex);
    QRCODE_DEBUG("Generated QR string: %s", buffer);
    return RT_EOK;

error_exit:
    buffer[pos] = '\0'; // Null-terminate the truncated string
    rt_mutex_release(qrcode_manager.mutex);
    QRCODE_DEBUG("Buffer overflow while generating QR string");
    return -RT_EFULL;
}

rt_err_t qrcode_data_clear_all(void)
{
    rt_err_t ret;
    
    if (!qrcode_manager.initialized) {
        return -RT_ERROR;
    }
    
    rt_mutex_take(qrcode_manager.mutex, RT_WAITING_FOREVER);
    
    memset(&qrcode_manager.header, 0, sizeof(struct qrcode_data_header));
    qrcode_manager.header.magic = QRCODE_DATA_MAGIC;
    memset(qrcode_manager.data_buffer, 0, QRCODE_DATA_SIZE - sizeof(struct qrcode_data_header));
    
    ret = qrcode_save_data();
    
    rt_mutex_release(qrcode_manager.mutex);
    
    QRCODE_DEBUG("Cleared all data, result=%d", ret);
    return ret;
}

bool qrcode_data_is_initialized(void)
{
    return qrcode_manager.initialized;
}