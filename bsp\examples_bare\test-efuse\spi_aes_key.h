/*
 * Copyright (c) 2022-2024, ArtInChip Technology Co., Ltd
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Authors: <AUTHORS>
 */
unsigned char spi_aes_key[] = {
  0x3e, 0x5a, 0x3b, 0x99, 0xfe, 0x87, 0x6f, 0x16, 0xe3, 0xb9, 0xd7, 0x67, 0x42, 0x35, 0xdc, 0x80
};
unsigned int spi_aes_key_len = 16;
unsigned char spi_nonce_key[] = {
  0xac, 0xac, 0x7c, 0xed, 0xdd, 0xf8, 0x07, 0xa3
};
unsigned int spi_nonce_key_len = 8;
unsigned char rotpk_bin[] = {
  0xad, 0xbf, 0x08, 0x7b, 0x00, 0x80, 0xb5, 0xab, 0x06, 0xed, 0x41, 0xe9, 0x23, 0x41, 0x9e, 0x45
};
unsigned int rotpk_bin_len = 16;
