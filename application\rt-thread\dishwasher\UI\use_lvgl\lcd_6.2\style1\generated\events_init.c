/*
* Copyright 2025 NXP
* NXP Proprietary. This software is owned or controlled by NXP and may only be used strictly in
* accordance with the applicable license terms. By expressly accepting such terms or by downloading, installing,
* activating and/or otherwise using the software, you are agreeing that you have read, and that you agree to
* comply with and are bound by, such license terms.  If you do not agree to be bound by the applicable license
* terms, then you may not retain, install, activate or otherwise use the software.
*/

#include "events_init.h"
#include <stdio.h>
#include "lvgl.h"

#if LV_USE_GUIDER_SIMULATOR && LV_USE_FREEMASTER
#include "freemaster_client.h"
#endif


static void lcd_standby_event_handler (lv_event_t *e)
{
    lv_event_code_t code = lv_event_get_code(e);
    switch (code) {
    case LV_EVENT_SCREEN_LOADED:
    {
        ui_animation(guider_ui.lcd_standby_roller_1, 500, 0, lv_obj_get_x(guider_ui.lcd_standby_roller_1), 650, &lv_anim_path_linear, 0, 0, 0, 0, (lv_anim_exec_xcb_t)lv_obj_set_x, NULL, NULL, NULL);
        break;
    }
    default:
        break;
    }
}

void events_init_lcd_standby (lv_ui *ui)
{
    lv_obj_add_event_cb(ui->lcd_standby, lcd_standby_event_handler, LV_EVENT_ALL, ui);
}

static void lcd_run_event_handler (lv_event_t *e)
{
    lv_event_code_t code = lv_event_get_code(e);
    switch (code) {
    case LV_EVENT_SCREEN_LOADED:
    {
        ui_animation(guider_ui.lcd_run_workmode_cont, 500, 0, lv_obj_get_y(guider_ui.lcd_run_workmode_cont), 20, &lv_anim_path_ease_in, 0, 0, 0, 0, (lv_anim_exec_xcb_t)lv_obj_set_y, NULL, NULL, NULL);
        break;
    }
    default:
        break;
    }
}

void events_init_lcd_run (lv_ui *ui)
{
    lv_obj_add_event_cb(ui->lcd_run, lcd_run_event_handler, LV_EVENT_ALL, ui);
}

static void lcd_parameter_event_handler (lv_event_t *e)
{
    lv_event_code_t code = lv_event_get_code(e);
    switch (code) {
    case LV_EVENT_SCREEN_LOADED:
    {
        ui_animation(guider_ui.lcd_parameter_list_1, 500, 0, lv_obj_get_x(guider_ui.lcd_parameter_list_1), 0, &lv_anim_path_linear, 0, 0, 0, 0, (lv_anim_exec_xcb_t)lv_obj_set_x, NULL, NULL, NULL);
        break;
    }
    default:
        break;
    }
}

void events_init_lcd_parameter (lv_ui *ui)
{
    lv_obj_add_event_cb(ui->lcd_parameter, lcd_parameter_event_handler, LV_EVENT_ALL, ui);
}

static void lcd_setting_event_handler (lv_event_t *e)
{
    lv_event_code_t code = lv_event_get_code(e);
    switch (code) {
    case LV_EVENT_SCREEN_LOADED:
    {
        ui_animation(guider_ui.lcd_setting_list_1, 500, 0, lv_obj_get_x(guider_ui.lcd_setting_list_1), 0, &lv_anim_path_linear, 0, 0, 0, 0, (lv_anim_exec_xcb_t)lv_obj_set_x, NULL, NULL, NULL);
        break;
    }
    default:
        break;
    }
}

void events_init_lcd_setting (lv_ui *ui)
{
    lv_obj_add_event_cb(ui->lcd_setting, lcd_setting_event_handler, LV_EVENT_ALL, ui);
}


void events_init(lv_ui *ui)
{

}
