/*
 * Copyright (c) 2022-2024, ArtInChip Technology Co., Ltd
 *
 * SPDX-License-Identifier: Apache-2.0
 */

#ifndef _AIC_DRV_WDT_H_
#define _AIC_DRV_WDT_H_


/* aic_wdt_cmd*/
#define RT_DEVICE_CTRL_WDT_SET_IRQ_TIMEOUT  (RT_DEVICE_CTRL_BASE(WDT) + 7)
#define RT_DEVICE_CTRL_WDT_IRQ_ENABLE       (RT_DEVICE_CTRL_BASE(WDT) + 8)
#define RT_DEVICE_CTRL_WDT_IRQ_DISABLE      (RT_DEVICE_CTRL_BASE(WDT) + 9)
#define RT_DEVICE_CTRL_WDT_SET_CLR_THD      (RT_DEVICE_CTRL_BASE(WDT) + 10)
#define RT_DEVICE_CTRL_WDT_SET_RST_CPU      (RT_DEVICE_CTRL_BASE(WDT) + 11)
#define RT_DEVICE_CTRL_WDT_SET_RST_SYS      (RT_DEVICE_CTRL_BASE(WDT) + 12)
#define RT_DEVICE_CTRL_WDT_GET_RST_EN       (RT_DEVICE_CTRL_BASE(WDT) + 13)
#define RT_DEVICE_CTRL_WDT_EN_REG           (RT_DEVICE_CTRL_BASE(WDT) + 14)
#endif /* _AIC_DRV_WDT_H_ */
