/*
 * Copyright (c) 2024, ArtInChip Technology CO.,LTD. All Rights Reserved.
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Author: <PERSON> <<EMAIL>>
 */

#ifndef _MBOX_REG_H_
#define _MBOX_REG_H_

#include "platform.h"

#define MB_FIFO_DEPTH   32

#define MB_BASE		BASE_CS_MBOX_CS2SE
#define MB_CTL_REG	(MB_BASE + 0x000)
#define MB_FIFO_CSR_REG	(MB_BASE + 0x004)
#define MB_IRQ_EN_REG	(MB_BASE + 0x008)
#define MB_IRQ_STS_REG	(MB_BASE + 0x00C)
#define MB_WMESG_REG	(MB_BASE + 0x010)
#define MB_WCOMP_REG	(MB_BASE + 0x014)
#define MB_RMESG_REG	(MB_BASE + 0x018)
#define MB_RCOMP_REG	(MB_BASE + 0x01C)
#define MB_VER_REG	(MB_BASE + 0xFFC)

#define MB_CTL_DBG_MODE_OFS	(0)
#define MB_CTL_DBG_MODE_MSK	(0x1U << MB_CTL_DBG_MODE_OFS)
#define MB_CTL_DBG_MODE_VAL(v)	(((v) << MB_CTL_DBG_MODE_OFS) & MB_CTL_DBG_MODE_MSK)

#define MB_CTL_COMP_MODE_OFS	1
#define MB_CTL_COMP_MODE_MSK	(0x1U << MB_CTL_COMP_MODE_OFS)
#define MB_CTL_COMP_MODE_VAL(v)	(((v) << MB_CTL_COMP_MODE_OFS) & MB_CTL_COMP_MODE_MSK)

#define MB_FIFO_TF_CNT_OFS	0
#define MB_FIFO_TF_CNT_MSK	(0xFFU << MB_FIFO_TF_CNT_OFS)
#define MB_FIFO_TF_CNT_VAL(v)	(((v) << MB_FIFO_TF_CNT_OFS) & MB_FIFO_TF_CNT_MSK)

#define MB_FIFO_TF_LVL_OFS	8
#define MB_FIFO_TF_LVL_MSK	(0x3FU << MB_FIFO_TF_LVL_OFS)
#define MB_FIFO_TF_LVL_VAL(v)	(((v) << MB_FIFO_TF_LVL_OFS) & MB_FIFO_TF_LVL_MSK)

#define MB_FIFO_TF_OVF_CTL_OFS	14
#define MB_FIFO_TF_OVF_CTL_MSK	(0x1U << MB_FIFO_TF_OVF_CTL_OFS)
#define MB_FIFO_TF_OVF_CTL_VAL(v)	(((v) << MB_FIFO_TF_OVF_CTL_OFS) & MB_FIFO_TF_OVF_CTL_MSK)

#define MB_FIFO_TF_RST_OFS	15
#define MB_FIFO_TF_RST_MSK	(0x1U << MB_FIFO_TF_RST_OFS)
#define MB_FIFO_TF_RST_VAL(v)	(((v) << MB_FIFO_TF_RST_OFS) & MB_FIFO_TF_RST_MSK)

#define MB_FIFO_RF_CNT_OFS	16
#define MB_FIFO_RF_CNT_MSK	(0xFFU << MB_FIFO_RF_CNT_OFS)
#define MB_FIFO_RF_CNT_VAL(v)	(((v) << MB_FIFO_RF_CNT_OFS) & MB_FIFO_RF_CNT_MSK)

#define MB_FIFO_RF_LVL_OFS	24
#define MB_FIFO_RF_LVL_MSK	(0x3FU << MB_FIFO_RF_LVL_OFS)
#define MB_FIFO_RF_LVL_VAL(v)	(((v) << MB_FIFO_RF_LVL_OFS) & MB_FIFO_RF_LVL_MSK)

#define MB_FIFO_RF_RST_OFS	31
#define MB_FIFO_RF_RST_MSK	(0x1U << MB_FIFO_RF_RST_OFS)
#define MB_FIFO_RF_RST_VAL(v)	(((v) << MB_FIFO_RF_RST_OFS) & MB_FIFO_RF_RST_MSK)

#define MB_IRQ_TF_EMP_OFS	0
#define MB_IRQ_TF_EMP_MSK	(0x1U << MB_IRQ_TF_EMP_OFS)
#define MB_IRQ_TF_EMP_VAL(v)	(((v) << MB_IRQ_TF_EMP_OFS) & MB_IRQ_TF_EMP_MSK)

#define MB_IRQ_TF_FULL_OFS	1
#define MB_IRQ_TF_FULL_MSK	(0x1U << MB_IRQ_TF_FULL_OFS)
#define MB_IRQ_TF_FULL_VAL(v)	(((v) << MB_IRQ_TF_FULL_OFS) & MB_IRQ_TF_FULL_MSK)

#define MB_IRQ_TF_OVF_OFS	2
#define MB_IRQ_TF_OVF_MSK	(0x1U << MB_IRQ_TF_OVF_OFS)
#define MB_IRQ_TF_OVF_VAL(v)	(((v) << MB_IRQ_TF_OVF_OFS) & MB_IRQ_TF_OVF_MSK)

#define MB_IRQ_SLV_COMP_OFS	3
#define MB_IRQ_SLV_COMP_MSK	(0x1U << MB_IRQ_SLV_COMP_OFS)
#define MB_IRQ_SLV_COMP_VAL(v)	(((v) << MB_IRQ_SLV_COMP_OFS) & MB_IRQ_SLV_COMP_MSK)

#define MB_IRQ_RF_EMP_OFS	8
#define MB_IRQ_RF_EMP_MSK	(0x1U << MB_IRQ_RF_EMP_OFS)
#define MB_IRQ_RF_EMP_VAL(v)	(((v) << MB_IRQ_RF_EMP_OFS) & MB_IRQ_RF_EMP_MSK)

#define MB_IRQ_RF_FULL_OFS	9
#define MB_IRQ_RF_FULL_MSK	(0x1U << MB_IRQ_RF_FULL_OFS)
#define MB_IRQ_RF_FULL_VAL(v)	(((v) << MB_IRQ_RF_FULL_OFS) & MB_IRQ_RF_FULL_MSK)

#define MB_IRQ_RF_UDR_OFS	10
#define MB_IRQ_RF_UDR_MSK	(0x1U << MB_IRQ_RF_UDR_OFS)
#define MB_IRQ_RF_UDR_VAL(v)	(((v) << MB_IRQ_RF_UDR_OFS) & MB_IRQ_RF_UDR_MSK)

#define MB_IRQ_MST_COMP_OFS	11
#define MB_IRQ_MST_COMP_MSK	(0x1U << MB_IRQ_MST_COMP_OFS)
#define MB_IRQ_MST_COMP_VAL(v)	(((v) << MB_IRQ_MST_COMP_OFS) & MB_IRQ_MST_COMP_MSK)

#endif
