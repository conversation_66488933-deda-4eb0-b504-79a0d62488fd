#include <rtthread.h>
#include <stdio.h>
#include "user_api.h"
#include "lvgl_msg.h"
#include <finsh.h>

/* 位操作宏定义 */
#define USER_SET_BIT(x, y) ((x) |= (1 << (y)))
#define USER_CLEAR_BIT(x, y) ((x) &= ~(1 << (y)))
#define USER_GET_BIT(x, y) (((x) >> (y)) & 0x01)

/* RT-Thread 信号量 */
static rt_mutex_t lv_mutex;

#define LV_ENTER() rt_mutex_take(lv_mutex, RT_WAITING_FOREVER)
#define LV_QUIT() rt_mutex_release(lv_mutex)
lv_ft_info_t lv_ft_info;


static inline void copy_string(char *_src, char **_des)
{
#if USE_MALLOC
    USER_FREE(*_des);
    *_des = NULL;
    *_des = USER_MALLOC((strlen(_src) + 1) * sizeof(char));
    /**/
    if (*_des)
    {
        strcpy(*_des, _src);
    }

#else
    uint8_t copylen = strlen(_src) < UI_STING_LENGTH - 1 ? strlen(_src) : UI_STING_LENGTH - 1;
    strncpy((char *)_des, _src, copylen);
    _des[copylen] = '\0';

#endif
}

/***********************页面切换相关*****************************/

static uint8_t g_active_screen_id = LCD_DEVELOPER;

uint8_t get_active_screen_id(void)
{
    return g_active_screen_id;
}

lv_obj_t *get_active_screen_ptr(void)
{
    lv_obj_t *act_scr = lv_scr_act();
    return act_scr;
}

static inline void refresh_sreen_id(enum DISP_PAGE _page)
{
    g_active_screen_id = _page;
}

void set_active_screen(enum DISP_PAGE _page)
{
    uint8_t old_page;
    bool *ole_page_del;

    if (_page == g_active_screen_id)
    {
        return;
    }

    old_page = g_active_screen_id;
    rt_kprintf("old page:%d\n", old_page);
    switch (old_page)
    {
    case LCD_START:
        ole_page_del = &guider_ui.lcd_start_del;
        break;

    case LCD_STANDBY:
        ole_page_del = &guider_ui.lcd_standby_del;
        break;

    case LCD_RUN:
        ole_page_del = &guider_ui.lcd_run_del;
        break;

    case LCD_PARAMETER:
        ole_page_del = &guider_ui.lcd_parameter_del;
        break;

    case LCD_SETTING:
        ole_page_del = &guider_ui.lcd_setting_del;
        break;

    case LCD_DEBUG:
        ole_page_del = &guider_ui.lcd_debug_del;
        break;

    case LCD_DEVELOPER:
        ole_page_del = &guider_ui.lcd_developer_del;
        break;

    default:
        break;
    };

    switch (_page)
    {
    case LCD_START:
        LV_LOG_USER("Handling LCD Start page.\n");
        ui_load_scr_animation(&guider_ui, &guider_ui.lcd_start, guider_ui.lcd_start_del, ole_page_del, setup_scr_lcd_start, LV_SCR_LOAD_ANIM_NONE, 200, 200, true, true);
        break;

    case LCD_STANDBY:
        LV_LOG_USER("Handling LCD Standby page.\n");
        ui_load_scr_animation(&guider_ui, &guider_ui.lcd_standby, guider_ui.lcd_standby_del, ole_page_del, setup_scr_lcd_standby, LV_SCR_LOAD_ANIM_NONE, 200, 200, true, true);
        break;

    case LCD_RUN:
        LV_LOG_USER("Handling LCD Run page.\n");
        ui_load_scr_animation(&guider_ui, &guider_ui.lcd_run, guider_ui.lcd_run_del, ole_page_del, setup_scr_lcd_run, LV_SCR_LOAD_ANIM_NONE, 200, 200, true, true);
        break;

    case LCD_PARAMETER:
        LV_LOG_USER("Handling LCD Parameter page.\n");
        ui_load_scr_animation(&guider_ui, &guider_ui.lcd_parameter, guider_ui.lcd_parameter_del, ole_page_del, setup_scr_lcd_parameter, LV_SCR_LOAD_ANIM_NONE, 200, 200, true, true);
        break;

    case LCD_SETTING:
        LV_LOG_USER("Handling LCD Software page.\n");
        ui_load_scr_animation(&guider_ui, &guider_ui.lcd_setting, guider_ui.lcd_setting_del, ole_page_del, setup_scr_lcd_setting, LV_SCR_LOAD_ANIM_NONE, 200, 200, true, true);
        break;

    case LCD_DEBUG:
        LV_LOG_USER("Handling LCD Debug page.\n");
        ui_load_scr_animation(&guider_ui, &guider_ui.lcd_debug, guider_ui.lcd_debug_del, ole_page_del, setup_scr_lcd_debug, LV_SCR_LOAD_ANIM_NONE, 200, 200, true, true);
        break;

    case LCD_DEVELOPER:
        LV_LOG_USER("Handling LCD Developer page.\n");
        ui_load_scr_animation(&guider_ui, &guider_ui.lcd_developer, guider_ui.lcd_developer_del, ole_page_del, setup_scr_lcd_developer, LV_SCR_LOAD_ANIM_NONE, 200, 200, true, true);
        break;

    default:
        // 处理未知或未定义的枚举值
        LV_LOG_USER("Unknown or undefined DISP_PAGE value.\n");
        break;
    }
}

/****************************************页面控件相关***************************************/

static inline void en_obj(lv_obj_t *_obj)
{
    USER_ASSERT_NULL(_obj);
    lv_obj_add_state(_obj, LV_STATE_DISABLED);
}

static inline void dis_obj(lv_obj_t *_obj)
{
    USER_ASSERT_NULL(_obj);
    lv_obj_clear_state(_obj, LV_STATE_DISABLED);
}

static inline void show_obj(lv_obj_t *_obj)
{
    USER_ASSERT_NULL(_obj);
    lv_obj_clear_flag(_obj, LV_OBJ_FLAG_HIDDEN);
}

static inline void hide_obj(lv_obj_t *_obj)
{
    USER_ASSERT_NULL(_obj);
    lv_obj_add_flag(_obj, LV_OBJ_FLAG_HIDDEN);
}

/*************************************LCD DEBUG****************************************/
typedef struct LCD_DEBUG_PARAM
{
    uint8_t focus_index;
    int v24_output[2];
    int temperature[3];
    uint32_t signal;
    uint32_t coil;
} LcdDebugParam_T;
static LcdDebugParam_T lcd_debug_param_t;
static lv_style_t lcd_debug_focus_style;
static lv_group_t *lcd_debug_focus_group = NULL;

static inline void lcd_debug_param_init(void)
{
    lcd_debug_param_t.focus_index = 0;
    memset(lcd_debug_param_t.v24_output, 0, sizeof(lcd_debug_param_t.v24_output));
    memset(lcd_debug_param_t.temperature, 0, sizeof(lcd_debug_param_t.temperature));
    lcd_debug_param_t.signal = 0;
    lcd_debug_param_t.coil = 0;
    LV_LOG_USER("v24_output:%d,%d\n", lcd_debug_param_t.v24_output[0], lcd_debug_param_t.v24_output[1]);
    LV_LOG_USER("temperature:%d,%d,%d\n", lcd_debug_param_t.temperature[0], lcd_debug_param_t.temperature[1], lcd_debug_param_t.temperature[2]);
}

static inline void lcd_debug_focus_init(void)
{

    ui_init_style(&lcd_debug_focus_style);
    lv_style_set_shadow_width(&lcd_debug_focus_style, 15);
    lv_style_set_shadow_color(&lcd_debug_focus_style, lv_color_hex(0xffffff));
    lv_style_set_shadow_opa(&lcd_debug_focus_style, 255);
    lv_style_set_shadow_spread(&lcd_debug_focus_style, 1);
    lv_style_set_shadow_ofs_x(&lcd_debug_focus_style, 0);
    lv_style_set_shadow_ofs_y(&lcd_debug_focus_style, 5);
}

static lv_obj_t *lcd_debug_change_index_to_foucus_obj(enum LCD_DEBUG_ITEM_INDEX _index)
{
    lv_obj_t *rt = NULL;
    switch (_index)
    {
    case LCD_DEBUG_24V_OUTPUT1_INDEX:
        rt = guider_ui.lcd_debug_label_29;
        break;
    case LCD_DEBUG_24V_OUTPUT2_INDEX:
        rt = guider_ui.lcd_debug_label_30;
        break;
    case LCD_DEBUG_TEMPERATURE1_INDEX:
        rt = guider_ui.lcd_debug_label_2;
        break;
    case LCD_DEBUG_TEMPERATURE2_INDEX:
        rt = guider_ui.lcd_debug_label_3;
        break;
    case LCD_DEBUG_TEMPERATURE3_INDEX:
        rt = guider_ui.lcd_debug_label_1;
        break;
    case LCD_DEBUG_SIGNAL1_INDEX:
        rt = guider_ui.lcd_debug_label_4;
        break;
    default:
        break;
    }
}

static inline void lcd_debug_restore_scene(void)
{
    lv_obj_t *obj;
    lcd_debug_set_24v_output(LCD_DEBUG_24V_OUTPUT1_INDEX, lcd_debug_param_t.v24_output[0]);
    lcd_debug_set_24v_output(LCD_DEBUG_24V_OUTPUT2_INDEX, lcd_debug_param_t.v24_output[1]);

    lcd_debug_set_temperature(LCD_DEBUG_TEMPERATURE1_INDEX, lcd_debug_param_t.temperature[0]);
    lcd_debug_set_temperature(LCD_DEBUG_TEMPERATURE2_INDEX, lcd_debug_param_t.temperature[1]);
    lcd_debug_set_temperature(LCD_DEBUG_TEMPERATURE3_INDEX, lcd_debug_param_t.temperature[2]);

    lcd_debug_set_signal(lcd_debug_param_t.signal);
    lcd_debug_set_coil(lcd_debug_param_t.coil);

    if (lcd_debug_focus_group != NULL)
    {
        lv_group_del(lcd_debug_focus_group);
        lcd_debug_focus_group = NULL;
    }

    lcd_debug_focus_group = lv_group_create();
    lv_group_add_obj(lcd_debug_focus_group, lv_scr_act());

    for (uint8_t i = 0; i <= LCD_DEBUG_ITEM_NUM; i++)
    {
        obj = lcd_debug_change_index_to_foucus_obj(i);
        if (obj != NULL)
        {
            lv_group_add_obj(lcd_debug_focus_group, obj);
            lv_obj_add_style(obj, &lcd_debug_focus_style, LV_PART_MAIN | LV_STATE_FOCUSED);
        }
    }
}

void lcd_debug_choose_focus_obj(enum LCD_DEBUG_ITEM_INDEX _index)
{

    lv_obj_t *obj;
    obj = lcd_debug_change_index_to_foucus_obj(_index);
    if (obj != NULL)
    {
        lcd_debug_param_t.focus_index = _index;
        lv_group_focus_obj(obj);
    }
}

int lcd_debug_get_current_focus_index(void)
{
    return lcd_debug_param_t.focus_index;
}

void lcd_debug_set_temperature(enum LCD_DEBUG_ITEM_INDEX _index, int _num)
{
    char buf[10];
    snprintf(buf, sizeof(buf), "%d", _num);
    switch (_index)
    {
    case LCD_DEBUG_TEMPERATURE1_INDEX:
        lcd_debug_param_t.v24_output[0] = _num;
        lv_label_set_text(guider_ui.lcd_debug_label_2, buf);
        break;
    case LCD_DEBUG_TEMPERATURE2_INDEX:
        lcd_debug_param_t.v24_output[1] = _num;
        lv_label_set_text(guider_ui.lcd_debug_label_3, buf);
        break;
    case LCD_DEBUG_TEMPERATURE3_INDEX:
        lcd_debug_param_t.v24_output[2] = _num;
        lv_label_set_text(guider_ui.lcd_debug_label_1, buf);
        break;
    default:
        break;
    }
}

void lcd_debug_set_24v_output(enum LCD_DEBUG_ITEM_INDEX _index, int _num)
{
    char buf[10];
    snprintf(buf, sizeof(buf), "%d档", _num);
    switch (_index)
    {
    case LCD_DEBUG_24V_OUTPUT1_INDEX:
        lcd_debug_param_t.v24_output[0] = _num;
        lv_label_set_text(guider_ui.lcd_debug_label_29, buf);
        break;
    case LCD_DEBUG_24V_OUTPUT2_INDEX:
        lcd_debug_param_t.v24_output[1] = _num;
        lv_label_set_text(guider_ui.lcd_debug_label_30, buf);
        break;
    default:
        break;
    }
}

static inline lv_obj_t *lcd_debug_change_signal_index_to_obj(enum LCD_DEBUG_ITEM_INDEX _index)
{
    lv_obj_t *rt = NULL;
    switch (_index)
    {
    case LCD_DEBUG_SIGNAL1_INDEX:
        rt = guider_ui.lcd_debug_sw_1;
        break;
    case LCD_DEBUG_SIGNAL2_INDEX:
        rt = guider_ui.lcd_debug_sw_2;
        break;
    case LCD_DEBUG_SIGNAL3_INDEX:
        rt = guider_ui.lcd_debug_sw_3;
        break;
    case LCD_DEBUG_SIGNAL4_INDEX:
        rt = guider_ui.lcd_debug_sw_4;
        break;
    case LCD_DEBUG_SIGNAL5_INDEX:
        rt = guider_ui.lcd_debug_sw_5;
        break;
    case LCD_DEBUG_SIGNAL6_INDEX:
        rt = guider_ui.lcd_debug_sw_6;
        break;

    default:
        break;
    }
    return rt;
}

void lcd_debug_set_signal(uint32_t _maskbit)
{
    int index;
    lv_obj_t *obj;
    lcd_debug_param_t.signal |= _maskbit;
    while (_maskbit != 0)
    {
        index = __rt_ffs(_maskbit) - 1;
        USER_CLEAR_BIT(_maskbit, index);
        obj = lcd_debug_change_signal_index_to_obj(index);
        if (obj != NULL)
        {
            lv_obj_add_state(obj, LV_STATE_CHECKED);
        }
    }
}

void lcd_debug_clear_signal(uint32_t _maskbit)
{
    int index;
    lv_obj_t *obj;
    lcd_debug_param_t.signal &= (~_maskbit);
    while (_maskbit != 0)
    {
        index = __rt_ffs(_maskbit) - 1;
        USER_CLEAR_BIT(_maskbit, index);
        obj = lcd_debug_change_signal_index_to_obj(index);
        if (obj != NULL)
        {
            lv_obj_clear_state(obj, LV_STATE_CHECKED);
        }
    }
}

static inline lv_obj_t *lcd_debug_change_coil_index_to_obj(enum LCD_DEBUG_ITEM_INDEX _index)
{
    lv_obj_t *rt = NULL;
    switch (_index)
    {
    case LCD_DEBUG_COIL1_INDEX:
        rt = guider_ui.lcd_debug_sw_7;
        break;
    case LCD_DEBUG_COIL2_INDEX:
        rt = guider_ui.lcd_debug_sw_8;
        break;
    case LCD_DEBUG_COIL3_INDEX:
        rt = guider_ui.lcd_debug_sw_9;
        break;
    case LCD_DEBUG_COIL4_INDEX:
        rt = guider_ui.lcd_debug_sw_10;
        break;
    case LCD_DEBUG_COIL5_INDEX:
        rt = guider_ui.lcd_debug_sw_11;
        break;
    case LCD_DEBUG_COIL6_INDEX:
        rt = guider_ui.lcd_debug_sw_12;
        break;
    case LCD_DEBUG_COIL7_INDEX:
        rt = guider_ui.lcd_debug_sw_13;
        break;
    case LCD_DEBUG_COIL8_INDEX:
        rt = guider_ui.lcd_debug_sw_14;
        break;
    case LCD_DEBUG_COIL9_INDEX:
        rt = guider_ui.lcd_debug_sw_15;
        break;
    default:
        break;
    }
    return rt;
}

void lcd_debug_set_coil(uint32_t _maskbit)
{
    int8_t index;
    lv_obj_t *obj;
    lcd_debug_param_t.coil |= _maskbit;
    while (_maskbit != 0)
    {
        index = __rt_ffs(_maskbit) - 1;
        USER_CLEAR_BIT(_maskbit, index);
        obj = lcd_debug_change_coil_index_to_obj(index);
        if (obj != NULL)
        {
            lv_obj_add_state(obj, LV_STATE_CHECKED);
        }
    }
}

void lcd_debug_clear_coil(uint32_t _maskbit)
{
    int8_t index;
    lv_obj_t *obj;
    lcd_debug_param_t.coil &= (~_maskbit);
    while (_maskbit != 0)
    {
        index = __rt_ffs(_maskbit) - 1;
        USER_CLEAR_BIT(_maskbit, index);
        obj = lcd_debug_change_coil_index_to_obj(index);
        if (obj != NULL)
        {
            lv_obj_clear_state(obj, LV_STATE_CHECKED);
        }
    }
}

void lcd_debug_setup_init(void)
{
    static uint8_t step = 0;
    refresh_sreen_id(LCD_DEBUG);
    switch (step)
    {
    case 0:
        lcd_debug_focus_init();
        lcd_debug_param_init();
        step = 1;
    default:
        lcd_debug_restore_scene();
        break;
    }
}

void lcd_debug_touch_cb(lv_event_t *e)
{
}
/*************************************LCD DEVELOPER*******************************/

static lv_style_t lcd_developer_label_style;

static const char *lcd_developer_default_string[LCD_DEVELOPER_ITEM_NUM] = {
#define LCD_DEVELOPER_X(name, string) string
    LCD_DEVELOPER_ITEM_LIST,
#undef LCD_DEVELOPER_X
};

typedef struct LCD_DEVELOPER_PARAM
{
    uint32_t user_define;
#if USE_MALLOC
    char *item_string[LCD_DEVELOPER_ITEM_NUM];
#else
    char item_string[LCD_DEVELOPER_ITEM_NUM][UI_STING_LENGTH];
#endif
} LcdDeveloperParam_T;

LcdDeveloperParam_T lcd_developer_param_t;
static inline void lcd_developer_param_init(void)
{
    lcd_developer_param_t.user_define = 0;
}

static inline void lcd_developer_style_init(void)
{
    ui_init_style(&lcd_developer_label_style);
    lv_style_set_border_width(&lcd_developer_label_style, 0);
    lv_style_set_radius(&lcd_developer_label_style, 0);
    lv_style_set_text_color(&lcd_developer_label_style, lv_color_hex(0xffffff));
    lv_style_set_text_font(&lcd_developer_label_style, lv_ft_info.font);
    lv_style_set_text_opa(&lcd_developer_label_style, 255);
    lv_style_set_text_letter_space(&lcd_developer_label_style, 2);
    lv_style_set_text_line_space(&lcd_developer_label_style, 0);
    lv_style_set_align(&lcd_developer_label_style, LV_TEXT_ALIGN_CENTER);
    lv_style_set_bg_opa(&lcd_developer_label_style, 0);
    lv_style_set_pad_top(&lcd_developer_label_style, 0);
    lv_style_set_pad_right(&lcd_developer_label_style, 0);
    lv_style_set_pad_bottom(&lcd_developer_label_style, 0);
    lv_style_set_pad_left(&lcd_developer_label_style, 0);
    lv_style_set_shadow_width(&lcd_developer_label_style, 0);
}

static inline void lcd_developer_restore_scene(void)
{
    lv_obj_set_flex_flow(guider_ui.lcd_developer_cont_1, LV_FLEX_FLOW_COLUMN);                                               // 垂直排列
    lv_obj_set_flex_align(guider_ui.lcd_developer_cont_1, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER); // 对齐方式

    for (uint8_t i = 0; i < LCD_DEVELOPER_ITEM_NUM; i++)
    {
        lv_obj_t *label = lv_label_create(guider_ui.lcd_developer_cont_1);
        lv_obj_add_style(label, &lcd_developer_label_style, LV_PART_MAIN | LV_STATE_DEFAULT);
        if (USER_GET_BIT(lcd_developer_param_t.user_define, i) == 1)
        {
            lv_label_set_text(label, lcd_developer_param_t.item_string[i]);
        }
        else
        {
            lv_label_set_text(label, lcd_developer_default_string[i]);
        }
    }
}

/*修改信息，在下次进入该页面时生效*/
void lcd_developer_change_label_txt(enum LCD_DEVELOPER_ITEM_INDEX _index, char *_string)
{
    copy_string(_string, (char **)&(lcd_developer_param_t.item_string[_index]));
    USER_SET_BIT(lcd_developer_param_t.user_define, _index);
}

void lcd_developer_setup_init(void)
{
    static uint8_t step = 0;
    refresh_sreen_id(LCD_DEVELOPER);
    switch (step)
    {
    case 0:
        lcd_developer_style_init();
        lcd_developer_param_init();
        step = 1;
    default:
        lcd_developer_restore_scene();

        break;
    }
}

void lcd_developer_touch_cb(lv_event_t *e)
{
}

/***********************************LCD PARAMETER**********************************/

static lv_style_t paramtype_focus_style;
static lv_style_t paramtype_default_style;
static lv_style_t paramvalue_default_style;
static lv_group_t *paramtype_group = NULL;

typedef struct LCD_PARAMETER_ITEM
{
#if USE_MALLOC
    char *param_string;
    char *value_string;
#else
    char param_string[UI_STING_LENGTH];
    char value_string[UI_STING_LENGTH];
#endif

} LcdParameterItem_T;

typedef struct LCD_PARAMETER_PARAM
{
    uint8_t param_index;
    uint8_t value_index;
    uint32_t param_user_define;
    uint32_t value_user_define;

    LcdParameterItem_T lcd_parameter_item_t[LCD_PARAMETER_ITEM_NUM];

} LcdParameter_T;

static LcdParameter_T lcd_parameter_t;

static inline void lcd_parameter_param_init(void)
{
    lcd_parameter_t.param_index = 0;
    lcd_parameter_t.value_index = 0;
    lcd_parameter_t.param_user_define = 0;
    lcd_parameter_t.value_user_define = 0;
}

static lv_obj_t *lv_param_obj_array[LCD_PARAMETER_ITEM_NUM];
static lv_obj_t *lv_value_obj_array[LCD_PARAMETER_ITEM_NUM];

#define LCD_PARAMETER_X(name, param, value) param
static char *param_default_string[LCD_PARAMETER_ITEM_NUM] = {LCD_PARAMETER_ITEM_LIST};
#undef LCD_PARAMETER_X

#define LCD_PARAMETER_X(name, param, value) value
static char *value_default_string[LCD_PARAMETER_ITEM_NUM] = {LCD_PARAMETER_ITEM_LIST};
#undef LCD_PARAMETER_X

void lcd_parameter_change_param_item(uint8_t _index, char *_string)
{

    if (_index >= LCD_PARAMETER_ITEM_NUM)
    {
        _index = LCD_PARAMETER_ITEM_NUM - 1;
    }

    copy_string(_string, (char **)&lcd_parameter_t.lcd_parameter_item_t[_index].param_string);
    USER_SET_BIT(lcd_parameter_t.param_user_define, _index);

    if (g_active_screen_id != LCD_PARAMETER)
        return;
    lv_label_set_text(lv_param_obj_array[_index], lcd_parameter_t.lcd_parameter_item_t[_index].param_string);
}

void lcd_parameter_change_value_item(uint8_t _index, char *_string)
{
    if (_index >= LCD_PARAMETER_ITEM_NUM)
    {
        _index = LCD_PARAMETER_ITEM_NUM - 1;
    }

    copy_string(_string, (char **)&lcd_parameter_t.lcd_parameter_item_t[_index].value_string);
    USER_SET_BIT(lcd_parameter_t.value_user_define, _index);

    if (g_active_screen_id != LCD_PARAMETER)
        return;
    lv_label_set_text(lv_value_obj_array[_index], lcd_parameter_t.lcd_parameter_item_t[_index].value_string);
}

void lcd_parameter_choose_param_value(int8_t _index)
{
    lv_obj_t *ptr;
    if (_index >= LCD_PARAMETER_ITEM_NUM)
    {
        _index = LCD_PARAMETER_ITEM_NUM - 1;
    }
    else if (_index < 0)
    {
        _index = 0;
    }

    lcd_parameter_t.param_index = _index;
    lcd_parameter_t.value_index = _index;

    if (g_active_screen_id != LCD_PARAMETER)
        return;

    ptr = lv_param_obj_array[_index];
    if (ptr != NULL)
        lv_group_focus_obj(ptr);

    ptr = lv_value_obj_array[_index];
    if (ptr != NULL)
        lv_obj_scroll_to_view(ptr, LV_ANIM_ON);
}

static inline void lcd_parameter_sytle_init(void)
{
    ui_init_style(&paramtype_default_style);
    lv_style_set_pad_top(&paramtype_default_style, 20);
    lv_style_set_pad_left(&paramtype_default_style, 0);
    lv_style_set_pad_right(&paramtype_default_style, 0);
    lv_style_set_pad_bottom(&paramtype_default_style, 0);
    lv_style_set_border_width(&paramtype_default_style, 1);
    lv_style_set_border_opa(&paramtype_default_style, 255);
    lv_style_set_border_color(&paramtype_default_style, lv_color_hex(0xffffff));
    lv_style_set_border_side(&paramtype_default_style, LV_BORDER_SIDE_BOTTOM);
    lv_style_set_text_color(&paramtype_default_style, lv_color_hex(0xffffff));
    lv_style_set_text_font(&paramtype_default_style, lv_ft_info.font);
    lv_style_set_text_opa(&paramtype_default_style, 255);
    lv_style_set_radius(&paramtype_default_style, 0);
    lv_style_set_transform_width(&paramtype_default_style, 0);
    lv_style_set_bg_opa(&paramtype_default_style, 255);
    lv_style_set_bg_color(&paramtype_default_style, lv_color_hex(0x383838));
    lv_style_set_bg_grad_dir(&paramtype_default_style, LV_GRAD_DIR_NONE);

    lv_style_init(&paramtype_focus_style);                                   // 初始化样式
    lv_style_set_bg_color(&paramtype_focus_style, lv_color_hex(0xdfdfdf));   // 设置背景颜色
    lv_style_set_text_color(&paramtype_focus_style, lv_color_hex(0x000000)); // 设置文本颜色

    lv_style_set_border_width(&paramvalue_default_style, 0);
    lv_style_set_radius(&paramvalue_default_style, 0);
    lv_style_set_text_color(&paramvalue_default_style, lv_color_hex(0xffffff));
    lv_style_set_text_font(&paramvalue_default_style,lv_ft_info.font);
    lv_style_set_text_opa(&paramvalue_default_style, 255);
    lv_style_set_text_letter_space(&paramvalue_default_style, 2);
    lv_style_set_text_line_space(&paramvalue_default_style, 0);
    lv_style_set_text_align(&paramvalue_default_style, LV_TEXT_ALIGN_CENTER);
    lv_style_set_bg_opa(&paramvalue_default_style, 0);
    lv_style_set_pad_top(&paramvalue_default_style, 25);
    lv_style_set_pad_right(&paramvalue_default_style, 0);
    lv_style_set_pad_bottom(&paramvalue_default_style, 0);
    lv_style_set_pad_left(&paramvalue_default_style, 0);
    lv_style_set_shadow_width(&paramvalue_default_style, 0);
}

static inline void lcd_parameter_restore_scene(void)
{
    lv_obj_t *ptr;
    char *string;
    if (paramtype_group != NULL)
    {
        lv_group_del(paramtype_group);
        paramtype_group = NULL;
    }
    paramtype_group = lv_group_create();
    LV_ASSERT_NULL(paramtype_group);

    for (uint8_t i = 0; i < LCD_PARAMETER_ITEM_NUM; i++)
    {
        if (USER_GET_BIT(lcd_parameter_t.param_user_define, i) == 1)
        {
            string = lcd_parameter_t.lcd_parameter_item_t[i].param_string;
        }
        else
        {
            string = param_default_string[i];
        }
        ptr = lv_list_add_text(guider_ui.lcd_parameter_list_1, string);
        LV_ASSERT_NULL(ptr);
        lv_param_obj_array[i] = ptr;
        lv_obj_add_style(ptr, &paramtype_default_style, LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_add_style(ptr, &paramtype_focus_style, LV_PART_MAIN | LV_STATE_FOCUSED);
        lv_obj_add_flag(ptr, LV_OBJ_FLAG_SCROLL_ON_FOCUS);
        lv_group_add_obj(paramtype_group, ptr);

        if (USER_GET_BIT(lcd_parameter_t.value_user_define, i) == 1)
        {
            string = lcd_parameter_t.lcd_parameter_item_t[i].value_string;
        }
        else
        {
            string = value_default_string[i];
        }
        ptr = lv_label_create(guider_ui.lcd_parameter_cont_1);
        LV_ASSERT_NULL(ptr);
        lv_value_obj_array[i] = ptr;
        lv_label_set_text(ptr, string);
        lv_label_set_long_mode(ptr, LV_LABEL_LONG_WRAP);
        lv_obj_set_pos(ptr, 0, 100 * i);
        lv_obj_set_size(ptr, 420, 100);
        lv_obj_add_style(ptr, &paramvalue_default_style, LV_PART_MAIN | LV_STATE_DEFAULT);
    }

    ptr = lv_value_obj_array[lcd_parameter_t.value_index];
    if (ptr != NULL)
    {
        lv_obj_set_scroll_snap_y(guider_ui.lcd_parameter_cont_1, LV_SCROLL_SNAP_CENTER);
        lv_obj_set_scroll_dir(guider_ui.lcd_parameter_cont_1, LV_DIR_VER);
        lv_obj_scroll_to_view(ptr, LV_ANIM_OFF);
    }
}
void lcd_parameter_setup_init(void)
{
    static uint8_t step = 0;
    refresh_sreen_id(LCD_PARAMETER);
    switch (step)
    {
    case 0:
        lcd_parameter_sytle_init();
        lcd_parameter_param_init();
        step = 1;

    default:
        lcd_parameter_restore_scene();
        break;
    }
}

void lcd_parameter_touch_cb(lv_event_t *e)
{
}

typedef struct LCD_RUN_PARAM
{
    uint8_t restore;
    uint8_t door_state;
    uint16_t progress;
    uint16_t workmode;
    uint16_t wash_water_level;
    uint16_t rinse_water_level;
    int16_t wash_preset_temperature;
    int16_t wash_actual_temperature;
    int16_t rinse_preset_temperature;
    int16_t rinse_actual_temperature;
    uint32_t focusbit;
} LcdRunParam_T;

static LcdRunParam_T lcd_run_param_t;
static lv_style_t lcd_run_focus_style;

static inline void lcd_run_param_init(void)
{
    memset(&lcd_run_param_t, 0, sizeof(lcd_run_param_t));
}

void lcd_run_choose_workmode(enum LCD_RUN_WORK_MODE _workmode)
{
    if (_workmode >= LCD_RUN_WORKMODE_NUM)
    {
        _workmode = LCD_RUN_WORKMODE_NUM - 1;
    }
    else if (_workmode < 0)
    {
        _workmode = 0;
    }

    if ((lcd_run_param_t.restore == 0) &&
        (lcd_run_param_t.workmode == _workmode))
        return;

    lcd_run_param_t.workmode = _workmode;

    if (g_active_screen_id != LCD_RUN)
        return;

    switch (_workmode)
    {
    case LCD_RUN_ECO_MODE:
        lv_obj_clear_state(guider_ui.lcd_run_workmode_label_1, LV_STATE_DISABLED);
        lv_obj_add_state(guider_ui.lcd_run_workmode_label_2, LV_STATE_DISABLED);
        lv_obj_add_state(guider_ui.lcd_run_workmode_label_3, LV_STATE_DISABLED);
        break;
    case LCD_RUN_NORMAL_MODE:
        lv_obj_add_state(guider_ui.lcd_run_workmode_label_1, LV_STATE_DISABLED);
        lv_obj_clear_state(guider_ui.lcd_run_workmode_label_2, LV_STATE_DISABLED);
        lv_obj_add_state(guider_ui.lcd_run_workmode_label_3, LV_STATE_DISABLED);
        break;
    case LCD_RUN_TURBO_MODE:
        lv_obj_add_state(guider_ui.lcd_run_workmode_label_1, LV_STATE_DISABLED);
        lv_obj_add_state(guider_ui.lcd_run_workmode_label_2, LV_STATE_DISABLED);
        lv_obj_clear_state(guider_ui.lcd_run_workmode_label_3, LV_STATE_DISABLED);
        break;
    default:
        lv_obj_add_state(guider_ui.lcd_run_workmode_label_1, LV_STATE_DISABLED);
        lv_obj_add_state(guider_ui.lcd_run_workmode_label_2, LV_STATE_DISABLED);
        lv_obj_add_state(guider_ui.lcd_run_workmode_label_3, LV_STATE_DISABLED);
        break;
    }
}
uint16_t lcd_run_get_workmode(void)
{
    return lcd_run_param_t.workmode;
}

static lv_obj_t *lcd_run_change_index_to_focus_obj(enum LCD_RUN_ITEM_INDEX _index)
{
    lv_obj_t *obj = NULL;
    switch (_index)
    {
    case LCD_RUN_WASH_TEMPERATURE_INDEX:
        obj = guider_ui.lcd_run_washtemp_cont;
        break;
    case LCD_RUN_RINSE_TEMPERATURE_INDEX:
        obj = guider_ui.lcd_run_rinsetemp_cont;
        break;
    case LCD_RUN_WORK_MODE_INDEX:
        obj = guider_ui.lcd_run_workmode_cont;
        break;
    case LCD_RUN_WASH_WATER_LEVEL_INDEX:
        obj = guider_ui.lcd_run_wash_cont;
        break;
    case LCD_RUN_RINSE_WATER_LEVEL_INDEX:
        obj = guider_ui.lcd_run_rinse_cont;
        break;

    default:
        break;
    }
    return obj;
}
static void lcd_run_en_focus(enum LCD_RUN_ITEM_INDEX _index)
{
    lv_obj_t *focus_obj;
    focus_obj = lcd_run_change_index_to_focus_obj(_index);
    if (focus_obj == NULL)
        return;
    switch (_index)
    {
    case LCD_RUN_WASH_TEMPERATURE_INDEX:
        lv_obj_set_style_shadow_width(focus_obj, 15, LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_text_color(guider_ui.lcd_run_washtemp_label_4, lv_color_hex(0xf5e041), LV_PART_MAIN | LV_STATE_DEFAULT);
        break;
    case LCD_RUN_RINSE_TEMPERATURE_INDEX:
        lv_obj_set_style_shadow_width(focus_obj, 15, LV_PART_MAIN | LV_STATE_DEFAULT);
        // lv_obj_set_style_text_color(guider_ui.lcd_run_rinsetemp_label_3, lv_color_hex(0xf5e041), LV_PART_MAIN | LV_STATE_DEFAULT);
        break;
    case LCD_RUN_WORK_MODE_INDEX:
        lv_obj_set_style_shadow_width(focus_obj, 15, LV_PART_MAIN | LV_STATE_DEFAULT);
        break;
    case LCD_RUN_WASH_WATER_LEVEL_INDEX:
        lv_obj_set_style_shadow_width(focus_obj, 15, LV_PART_MAIN | LV_STATE_DEFAULT);
        break;
    case LCD_RUN_RINSE_WATER_LEVEL_INDEX:
        lv_obj_set_style_shadow_width(focus_obj, 15, LV_PART_MAIN | LV_STATE_DEFAULT);
        break;

    default:
        break;
    }
}

static void lcd_run_dis_focus(enum LCD_RUN_ITEM_INDEX _index)
{
    lv_obj_t *focus_obj;
    focus_obj = lcd_run_change_index_to_focus_obj(_index);
    if (focus_obj == NULL)
        return;
    switch (_index)
    {
    case LCD_RUN_WASH_TEMPERATURE_INDEX:
        lv_obj_set_style_shadow_width(focus_obj, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_text_color(guider_ui.lcd_run_washtemp_label_4, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
        break;
    case LCD_RUN_RINSE_TEMPERATURE_INDEX:
        lv_obj_set_style_shadow_width(focus_obj, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
        // lv_obj_set_style_text_color(guider_ui.lcd_run_rinsetemp_label_3, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
        break;
    case LCD_RUN_WORK_MODE_INDEX:
        lv_obj_set_style_shadow_width(focus_obj, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
        break;
    case LCD_RUN_WASH_WATER_LEVEL_INDEX:
        lv_obj_set_style_shadow_width(focus_obj, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
        break;
    case LCD_RUN_RINSE_WATER_LEVEL_INDEX:
        lv_obj_set_style_shadow_width(focus_obj, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
        break;

    default:
        break;
    }
}

void lcd_run_choose_focus_obj(enum LCD_RUN_ITEM_INDEX _index)
{
    if (_index < 0)
    {
        _index = 0;
    }
    else if (_index >= LCD_RUN_ITEM_NUM)
    {
        _index = LCD_RUN_ITEM_NUM - 1;
    }

    if (lcd_run_param_t.focusbit == (1 << _index))
        return;

    lcd_run_param_t.focusbit = 1 << _index;

    if (g_active_screen_id != LCD_RUN)
        return;

    for (uint8_t i = 0; i < LCD_RUN_ITEM_NUM; i++)
    {
        if (USER_GET_BIT(lcd_run_param_t.focusbit, i) == 1)
        {
            lcd_run_en_focus(i);
        }
        else
        {
            lcd_run_dis_focus(i);
        }
    }
}

uint32_t lcd_run_get_focus_maskbit(void)
{
    return lcd_run_param_t.focusbit;
}

void lcd_run_add_focus(enum LCD_RUN_ITEM_BITMASK _maskbit)
{
    int8_t index;
    uint32_t change;
    _maskbit &= LCD_RUN_ALL_BITMASK;
    if (lcd_run_param_t.restore == 0)
    {
        change = lcd_run_param_t.focusbit ^ (lcd_run_param_t.focusbit | _maskbit);
        lcd_run_param_t.focusbit |= _maskbit;
    }
    else
    {
        change = _maskbit;
    }
    if (g_active_screen_id != LCD_RUN)
        return;
    while (change != 0)
    {
        index = __rt_ffs(change) - 1;
        USER_CLEAR_BIT(change, index);
        lcd_run_en_focus(index);
    }
}

void lcd_run_clear_focus(enum LCD_RUN_ITEM_BITMASK _maskbit)
{
    int8_t index;
    uint32_t change;

    _maskbit &= LCD_RUN_ALL_BITMASK;

    change = lcd_run_param_t.focusbit ^ (lcd_run_param_t.focusbit & (~_maskbit));
    lcd_run_param_t.focusbit &= (~_maskbit);

    if (g_active_screen_id != LCD_RUN)
        return;

    while (change != 0)
    {
        index = __rt_ffs(change) - 1;
        USER_CLEAR_BIT(change, index);
        lcd_run_dis_focus(index);
    }
}

void lcd_run_set_wash_water_level(enum LCD_RUN_WATER_LEVEL _level)
{
    lcd_run_param_t.wash_water_level = _level;
    if (g_active_screen_id != LCD_RUN)
        return;
    switch (_level)
    {
    case LCD_RUN_WATER_LOW:
        lv_label_set_text(guider_ui.lcd_run_wash_label, "水满");
        lv_obj_set_style_text_color(guider_ui.lcd_run_wash_label, lv_color_hex(0xffbc54), LV_PART_MAIN | LV_STATE_DEFAULT);
        break;
    case LCD_RUN_WATER_NORMAL:
        lv_label_set_text(guider_ui.lcd_run_wash_label, "正常");
        lv_obj_set_style_text_color(guider_ui.lcd_run_wash_label, lv_color_hex(0xdfff7a), LV_PART_MAIN | LV_STATE_DEFAULT);
        break;
    case LCD_RUN_WATER_FULL:
        lv_label_set_text(guider_ui.lcd_run_wash_label, "缺水");
        lv_obj_set_style_text_color(guider_ui.lcd_run_wash_label, lv_color_hex(0xff0000), LV_PART_MAIN | LV_STATE_DEFAULT);
        break;

    default:
        break;
    }
}

void lcd_run_set_rinse_water_level(enum LCD_RUN_WATER_LEVEL _level)
{
    lcd_run_param_t.rinse_water_level = _level;
    if (g_active_screen_id != LCD_RUN)
        return;
    switch (_level)
    {
    case LCD_RUN_WATER_LOW:
        lv_label_set_text(guider_ui.lcd_run_rinse_label, "水满");
        lv_obj_set_style_text_color(guider_ui.lcd_run_rinse_label, lv_color_hex(0xffbc54), LV_PART_MAIN | LV_STATE_DEFAULT);
        break;
    case LCD_RUN_WATER_NORMAL:
        lv_label_set_text(guider_ui.lcd_run_rinse_label, "正常");
        lv_obj_set_style_text_color(guider_ui.lcd_run_rinse_label, lv_color_hex(0xdfff7a), LV_PART_MAIN | LV_STATE_DEFAULT);
        break;
    case LCD_RUN_WATER_FULL:
        lv_label_set_text(guider_ui.lcd_run_rinse_label, "缺水");
        lv_obj_set_style_text_color(guider_ui.lcd_run_rinse_label, lv_color_hex(0xff0000), LV_PART_MAIN | LV_STATE_DEFAULT);
        break;

    default:
        break;
    }
}

void lcd_run_set_wash_preset_temperature(int16_t _temperature)
{
    char buf[10];
    lcd_run_param_t.wash_preset_temperature = _temperature;
    if (g_active_screen_id != LCD_RUN)
    {
        return;
    }
    snprintf(buf, sizeof(buf), "%d", _temperature);
    lv_label_set_text(guider_ui.lcd_run_washtemp_label_4, buf);
}

void lcd_run_set_wash_actual_temperature(int16_t _temperature)
{
    char buf[10];
    lcd_run_param_t.wash_actual_temperature = _temperature;
    if (g_active_screen_id != LCD_RUN)
        return;
    snprintf(buf, sizeof(buf), "%d", _temperature);
    lv_label_set_text(guider_ui.lcd_run_washtemp_label_2, buf);
}

void lcd_run_set_rinse_preset_temperature(int16_t _temperature)
{
    char buf[10];
    lcd_run_param_t.rinse_preset_temperature = _temperature;
    if (g_active_screen_id != LCD_RUN)
        return;
    snprintf(buf, sizeof(buf), "%d", _temperature);
    //  lv_label_set_text(guider_ui.lcd_run_rinsetemp_label_3, buf);
}

void lcd_run_set_rinse_actual_temperature(int16_t _temperature)
{
    char buf[10];
    lcd_run_param_t.rinse_actual_temperature = _temperature;
    if (g_active_screen_id != LCD_RUN)
        return;
    snprintf(buf, sizeof(buf), "%d", _temperature);
    lv_label_set_text(guider_ui.lcd_run_rinsetemp_label_4, buf);
}

void lcd_run_setdoor_state(uint8_t _onoff)
{
    lcd_run_param_t.door_state = _onoff;
    if (g_active_screen_id != LCD_RUN)
        return;
    if (_onoff == 0)
    {
        lv_label_set_text(guider_ui.lcd_run_door_label, "机门关闭");
    }
    else
    {
        lv_label_set_text(guider_ui.lcd_run_door_label, "机门打开");
    }
}

void lcd_run_set_progress(uint16_t _num)
{
    lv_draw_img_dsc_t lcd_run_canvas_1_img_dsc;
    if (_num > 12)
    {
        return;
    }
    lcd_run_param_t.progress = _num;

    if (g_active_screen_id != LCD_RUN)
        return;

    lv_canvas_fill_bg(guider_ui.lcd_run_wait_canvas, lv_color_hex(0xffffff), 0);
    lv_draw_img_dsc_init(&lcd_run_canvas_1_img_dsc);

    switch (_num)
    {
    case 0:
        lv_canvas_draw_img(guider_ui.lcd_run_wait_canvas, 0, 0, LVGL_PATH(14.png), &lcd_run_canvas_1_img_dsc);
        break;
    case 1:
        lv_canvas_draw_img(guider_ui.lcd_run_wait_canvas, 0, 0, LVGL_PATH(15.png), &lcd_run_canvas_1_img_dsc);
        break;
    case 2:
        lv_canvas_draw_img(guider_ui.lcd_run_wait_canvas, 0, 0, LVGL_PATH(16.png), &lcd_run_canvas_1_img_dsc);
        break;
    case 3:
        lv_canvas_draw_img(guider_ui.lcd_run_wait_canvas, 0, 0, LVGL_PATH(17.png), &lcd_run_canvas_1_img_dsc);
        break;
    case 4:
        lv_canvas_draw_img(guider_ui.lcd_run_wait_canvas, 0, 0, LVGL_PATH(18.png), &lcd_run_canvas_1_img_dsc);
        break;
    case 5:
        lv_canvas_draw_img(guider_ui.lcd_run_wait_canvas, 0, 0, LVGL_PATH(19.png), &lcd_run_canvas_1_img_dsc);
        break;
    case 6:
        lv_canvas_draw_img(guider_ui.lcd_run_wait_canvas, 0, 0, LVGL_PATH(20.png), &lcd_run_canvas_1_img_dsc);
        break;
    case 7:
        lv_canvas_draw_img(guider_ui.lcd_run_wait_canvas, 0, 0, LVGL_PATH(21.png), &lcd_run_canvas_1_img_dsc);
        break;
    case 8:
        lv_canvas_draw_img(guider_ui.lcd_run_wait_canvas, 0, 0, LVGL_PATH(22.png), &lcd_run_canvas_1_img_dsc);
        break;
    case 9:
        lv_canvas_draw_img(guider_ui.lcd_run_wait_canvas, 0, 0, LVGL_PATH(23.png), &lcd_run_canvas_1_img_dsc);
        break;
    case 10:
        lv_canvas_draw_img(guider_ui.lcd_run_wait_canvas, 0, 0, LVGL_PATH(24.png), &lcd_run_canvas_1_img_dsc);
        break;
    case 11:
        lv_canvas_draw_img(guider_ui.lcd_run_wait_canvas, 0, 0, LVGL_PATH(25.png), &lcd_run_canvas_1_img_dsc);
        break;
    case 12:
        lv_canvas_draw_img(guider_ui.lcd_run_wait_canvas, 0, 0, LVGL_PATH(26.png), &lcd_run_canvas_1_img_dsc);
        break;

    default:
        break;
    }
}

void lcd_run_show_warning(char *_string)
{
    lv_obj_clear_flag(guider_ui.lcd_run_warning_cont, LV_OBJ_FLAG_HIDDEN);
    lv_label_set_text(guider_ui.lcd_run_warning_label, _string);
}

void lcd_run_hide_warning(void)
{
    lv_obj_add_flag(guider_ui.lcd_run_warning_cont, LV_OBJ_FLAG_HIDDEN);
}

static inline void lcd_run_restore_scene(void)
{
    lcd_run_choose_workmode(lcd_run_param_t.workmode);
    lcd_run_add_focus(lcd_run_param_t.focusbit);
    lcd_run_set_wash_water_level(lcd_run_param_t.wash_water_level);
    lcd_run_set_rinse_water_level(lcd_run_param_t.rinse_water_level);
    lcd_run_set_wash_preset_temperature(lcd_run_param_t.wash_preset_temperature);
    lcd_run_set_wash_actual_temperature(lcd_run_param_t.wash_actual_temperature);

    lcd_run_set_rinse_preset_temperature(lcd_run_param_t.rinse_preset_temperature);
    lcd_run_set_rinse_actual_temperature(lcd_run_param_t.rinse_actual_temperature);
    lcd_run_setdoor_state(lcd_run_param_t.door_state);
    lcd_run_set_progress(lcd_run_param_t.progress);

    for (uint8_t i = 0; i < LCD_RUN_ITEM_NUM; i++)
    {
        lv_obj_t *focus_obj;
        focus_obj = lcd_run_change_index_to_focus_obj(i);
        if (focus_obj != NULL)
            lv_obj_add_style(focus_obj, &lcd_run_focus_style, LV_PART_MAIN | LV_STATE_DEFAULT);
    }
}

static inline void lcd_run_style_init()
{
    ui_init_style(&lcd_run_focus_style);
    lv_style_set_shadow_width(&lcd_run_focus_style, 15);
    lv_style_set_shadow_color(&lcd_run_focus_style, lv_color_hex(0xebff00));
    lv_style_set_shadow_opa(&lcd_run_focus_style, 255);
    lv_style_set_shadow_spread(&lcd_run_focus_style, 1);
    lv_style_set_shadow_ofs_x(&lcd_run_focus_style, 0);
    lv_style_set_shadow_ofs_y(&lcd_run_focus_style, 5);
}
void lcd_run_setup_init(void)
{
    static uint8_t step = 0;
    refresh_sreen_id(LCD_RUN);
    switch (step)
    {
    case 0:
        lcd_run_style_init();
        lcd_run_param_init();
        step = 1;
    default:
        lcd_run_param_t.restore = 1;
        lcd_run_restore_scene();
        lcd_run_param_t.restore = 0;
        break;
    }
}

void lcd_run_touch_cb(lv_event_t *e)
{
}

/**************************LCD SETTING*****************************/

typedef struct LCD_SETTING_ITEM
{
    int item_index;
#if USE_MALLOC
    char *item_string;
#else
    char item_string[UI_STING_LENGTH];
#endif
} LcdSettingItem_T;

static lv_obj_t *lcd_setting_parent_param_obj[LCD_SETTING_PARENT_PARAM_NUM];
static lv_obj_t *lcd_setting_sub_param_obj[LCD_SETTING_SUB_PARAM_NUM];
static lv_obj_t *lcd_setting_value_obj[LCD_SETTING_PARENT_PARAM_NUM + 1];

typedef struct LCD_SETTING_PARAM
{
    uint8_t current_index;
    LcdSettingItem_T lcd_setting_sub_param_t[LCD_SETTING_SUB_PARAM_NUM];
} LcdSettingParam_T;

static LcdSettingParam_T lcd_setting_param_t = {0,
#define LCD_SETTING_X(parent, name, string) {parent, string}
                                                {LCD_SETTING_SUB_PARAM_LIST}
#undef LCD_SETTING_X
};

static lv_style_t lcd_setting_sub_param_style;
static lv_style_t lcd_setting_sub_param_focus_style;
static lv_style_t lcd_setting_sub_value_style;
static lv_group_t *lcd_setting_sub_param_group = NULL;

static inline void lcd_setting_param_init(void)
{
    lcd_setting_param_t.current_index = 0;
}

static inline void lcd_setting_style_init(void)
{
    ui_init_style(&lcd_setting_sub_param_style);
    lv_style_set_pad_top(&lcd_setting_sub_param_style, 5);
    lv_style_set_pad_left(&lcd_setting_sub_param_style, 5);
    lv_style_set_pad_right(&lcd_setting_sub_param_style, 5);
    lv_style_set_pad_bottom(&lcd_setting_sub_param_style, 5);
    lv_style_set_border_width(&lcd_setting_sub_param_style, 0);
    lv_style_set_text_color(&lcd_setting_sub_param_style, lv_color_hex(0xffffff));
    // lv_style_set_text_font(&lcd_setting_sub_param_style, &lv_customer_font_FY_20);
    lv_style_set_text_opa(&lcd_setting_sub_param_style, 255);
    lv_style_set_radius(&lcd_setting_sub_param_style, 3);
    lv_style_set_transform_width(&lcd_setting_sub_param_style, 0);
    lv_style_set_bg_opa(&lcd_setting_sub_param_style, 255);
    lv_style_set_bg_color(&lcd_setting_sub_param_style, lv_color_hex(0x383838));
    lv_style_set_bg_grad_dir(&lcd_setting_sub_param_style, LV_GRAD_DIR_NONE);

    ui_init_style(&lcd_setting_sub_param_focus_style);
    lv_style_set_pad_top(&lcd_setting_sub_param_focus_style, 5);
    lv_style_set_pad_left(&lcd_setting_sub_param_focus_style, 5);
    lv_style_set_pad_right(&lcd_setting_sub_param_focus_style, 5);
    lv_style_set_pad_bottom(&lcd_setting_sub_param_focus_style, 5);
    lv_style_set_border_width(&lcd_setting_sub_param_focus_style, 0);
    lv_style_set_text_color(&lcd_setting_sub_param_focus_style, lv_color_hex(0x383838));
    // lv_style_set_text_font(&lcd_setting_sub_param_focus_style, &lv_customer_font_FY_20);
    lv_style_set_text_opa(&lcd_setting_sub_param_focus_style, 255);
    lv_style_set_radius(&lcd_setting_sub_param_focus_style, 3);
    lv_style_set_transform_width(&lcd_setting_sub_param_focus_style, 0);
    lv_style_set_bg_opa(&lcd_setting_sub_param_focus_style, 255);
    lv_style_set_bg_color(&lcd_setting_sub_param_focus_style, lv_color_hex(0xc8d257));
    lv_style_set_bg_grad_dir(&lcd_setting_sub_param_focus_style, LV_GRAD_DIR_NONE);

    ui_init_style(&lcd_setting_sub_value_style);
    lv_style_set_border_width(&lcd_setting_sub_value_style, 0);
    lv_style_set_radius(&lcd_setting_sub_value_style, 0);
    lv_style_set_text_color(&lcd_setting_sub_value_style, lv_color_hex(0xffffff));
    lv_style_set_text_font(&lcd_setting_sub_value_style,lv_ft_info.font);
    lv_style_set_text_opa(&lcd_setting_sub_value_style, 255);
    lv_style_set_text_letter_space(&lcd_setting_sub_value_style, 2);
    lv_style_set_text_line_space(&lcd_setting_sub_value_style, 0);
    lv_style_set_text_align(&lcd_setting_sub_value_style, LV_TEXT_ALIGN_CENTER);
    lv_style_set_bg_opa(&lcd_setting_sub_value_style, 0);
    lv_style_set_pad_top(&lcd_setting_sub_value_style, 0);
    lv_style_set_pad_right(&lcd_setting_sub_value_style, 0);
    lv_style_set_pad_bottom(&lcd_setting_sub_value_style, 0);
    lv_style_set_pad_left(&lcd_setting_sub_value_style, 0);
    lv_style_set_shadow_width(&lcd_setting_sub_value_style, 0);
}

static inline void lcd_setting_restore_scene(void)
{
    int parent_local;
    int parent_index;
    lv_obj_t *obj;
    char *type_string;
    char *value_string;
    char buffer[UI_STING_LENGTH];
    uint32_t parent_param_cnt;

    parent_param_cnt = lv_obj_get_child_cnt(guider_ui.lcd_setting_list_1);
    LV_LOG_USER("parent_param_cnt:%d\n", parent_param_cnt);
    /*为每个大类创建一个文本框，并额外创建一个文本框置于末尾*/
    for (uint8_t i = 0; i <= LCD_SETTING_PARENT_PARAM_NUM; i++)
    {
        if (i < parent_param_cnt)
        {
            lcd_setting_parent_param_obj[i] = lv_obj_get_child(guider_ui.lcd_setting_list_1, i);
        }

        obj = lv_label_create(guider_ui.lcd_setting_cont_1);
        lcd_setting_value_obj[i] = obj;
        lv_label_set_long_mode(obj, LV_LABEL_LONG_WRAP);
        lv_obj_set_pos(obj, 0, 240 * i);
        lv_obj_set_size(obj, 420, 100);
        lv_obj_add_style(obj, &lcd_setting_sub_value_style, LV_PART_MAIN | LV_STATE_DEFAULT);
    }

    if (lcd_setting_sub_param_group != NULL)
    {
        lv_group_del(lcd_setting_sub_param_group);
        lcd_setting_sub_param_group = NULL;
    }
    lcd_setting_sub_param_group = lv_group_create();

    /*将所有子类添加到list控件中*/
    for (int8_t i = LCD_SETTING_SUB_PARAM_NUM - 1; i >= 0; i--)
    {
        parent_index = lcd_setting_param_t.lcd_setting_sub_param_t[i].item_index;
        type_string = lcd_setting_param_t.lcd_setting_sub_param_t[i].item_string;

        if (parent_index < parent_param_cnt)
        {
            obj = lcd_setting_parent_param_obj[parent_index];
            parent_local = lv_obj_get_index(obj);
        }
        else
        {
            parent_index = LCD_SETTING_PARENT_PARAM_NUM;
            parent_local = LCD_SETTING_SUB_PARAM_NUM + LCD_SETTING_PARENT_PARAM_NUM + 1;
        }
        obj = lv_list_add_text(guider_ui.lcd_setting_list_1, type_string);
        lv_obj_add_style(obj, &lcd_setting_sub_param_style, LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_add_style(obj, &lcd_setting_sub_param_focus_style, LV_PART_MAIN | LV_STATE_FOCUSED);
        lv_obj_add_flag(obj, LV_OBJ_FLAG_SCROLL_ON_FOCUS);
        lv_group_add_obj(lcd_setting_sub_param_group, obj);
        lcd_setting_sub_param_obj[i] = obj;
        lv_obj_move_to_index(obj, parent_local + 1);
    }

    lcd_setting_choose_param(lcd_setting_param_t.current_index);
}

/*改变当前文本框中的内容*/
void lcd_setting_change_current_value(char *_string)
{
    lv_obj_t *obj;
    uint8_t obj_index;
    obj_index = lcd_setting_param_t.lcd_setting_sub_param_t[lcd_setting_param_t.current_index].item_index;
    obj = lcd_setting_value_obj[obj_index];
    lv_label_set_text(obj, _string);
}
/*绑定参数和文本框内容*/
void lcd_setting_bind()
{
    lv_obj_t *param_obj;
    lv_obj_t *value_obj;
    char *value_string;
    char *param_string;
    char newstring[UI_STING_LENGTH];
    char parambuffer[UI_STING_LENGTH];
    uint8_t value_index = lcd_setting_param_t.lcd_setting_sub_param_t[lcd_setting_param_t.current_index].item_index;

    value_obj = lcd_setting_value_obj[value_index];
    param_obj = lcd_setting_sub_param_obj[lcd_setting_param_t.current_index];

    value_string = lv_label_get_text(value_obj);
    param_string = lv_label_get_text(param_obj);

    sscanf(param_string, "%[^:]", parambuffer);
    snprintf(newstring, sizeof(newstring), "%s:%s", parambuffer, value_string);

    memset(lcd_setting_param_t.lcd_setting_sub_param_t[lcd_setting_param_t.current_index].item_string, 0, sizeof(lcd_setting_param_t.lcd_setting_sub_param_t[lcd_setting_param_t.current_index].item_string));
    copy_string(newstring, (char **)&lcd_setting_param_t.lcd_setting_sub_param_t[lcd_setting_param_t.current_index].item_string);
    lv_label_set_text(param_obj, newstring);
}
/*修改指定参数内容*/
void lcd_settting_change_param_string(enum LCD_SETTING_SUB_PARAM_INDEX _index, char *_string)
{
    lv_obj_t *param_obj;
    if (_index >= LCD_SETTING_SUB_PARAM_NUM || _index < 0)
        return;
    param_obj = lcd_setting_sub_param_obj[_index];
    copy_string(_string, (char **)&(lcd_setting_param_t.lcd_setting_sub_param_t[_index].item_string));
    lv_label_set_text(param_obj, _string);
}
/*跳转到指定参数*/
void lcd_setting_choose_param(enum LCD_SETTING_SUB_PARAM_INDEX _typeindex)
{
    lv_obj_t *focus_obj;
    lv_obj_t *label_obj;
    uint8_t parent_index;
    char value_string[UI_STING_LENGTH];
    char *param_string;

    if (_typeindex >= LCD_SETTING_SUB_PARAM_NUM)
    {
        _typeindex = LCD_SETTING_SUB_PARAM_NUM - 1;
    }
    else if (_typeindex < 0)
    {
        _typeindex = 0;
    }

    parent_index = lcd_setting_param_t.lcd_setting_sub_param_t[_typeindex].item_index;
    if (parent_index >= LCD_SETTING_PARENT_PARAM_NUM)
    {
        parent_index = LCD_SETTING_PARENT_PARAM_NUM;
    }

    lcd_setting_param_t.current_index = _typeindex;

    focus_obj = lcd_setting_sub_param_obj[_typeindex];
    label_obj = lcd_setting_value_obj[parent_index];

    param_string = lv_label_get_text(focus_obj);
    sscanf(param_string, "%*[^:]:%s", value_string);

    lv_group_focus_obj(focus_obj);
    lv_obj_scroll_to_view(label_obj, LV_ANIM_ON);
    lv_label_set_text(label_obj, value_string);
}

void lcd_setting_setup_init(void)
{
    static uint8_t step = 0;
    refresh_sreen_id(LCD_SETTING);
    switch (step)
    {
    case 0:
        lcd_setting_style_init();
        lcd_setting_param_init();
        lv_obj_set_scroll_snap_y(guider_ui.lcd_setting_cont_1, LV_SCROLL_SNAP_CENTER);
        lv_obj_set_scroll_dir(guider_ui.lcd_setting_cont_1, LV_DIR_VER);

        step = 1;

    default:
        lcd_setting_restore_scene();
        break;
    }
}

void lcd_setting_touch_cb(lv_event_t *e)
{
}
/***************************LCD STANDBY****************************/
typedef struct LCD_STANDBY_PARAM
{
    uint8_t roller_index;
} LcdStandbyParam_T;

static LcdStandbyParam_T lcd_standby_param_t;

static inline void lcd_standby_param_init(void)
{
    lcd_standby_param_t.roller_index = 0;
}

void lcd_standby_set_roller_index(enum LCD_STANDBY_ROLLER_INDEX _index)
{
    if (_index > LCD_STANDBY_NUM)
    {
        _index = LCD_STANDBY_NUM - 1;
    }
    else if (_index < 0)
    {
        _index = -1;
    }
    lcd_standby_param_t.roller_index = _index;
    if (g_active_screen_id != LCD_STANDBY)
    {
        return;
    }
    lv_roller_set_selected(guider_ui.lcd_standby_roller_1, _index, LV_ANIM_ON);
}

uint8_t lcd_standby_get_roller_index(void)
{
    return lcd_standby_param_t.roller_index;
}

void lcd_standby_setup_init(void)
{
    static uint8_t step = 0;
    refresh_sreen_id(LCD_STANDBY);
    switch (step)
    {
    case 0:
        lcd_standby_param_init();
        step = 1;

    default:
        lv_roller_set_selected(guider_ui.lcd_standby_roller_1, lcd_standby_param_t.roller_index, LV_ANIM_OFF);
        break;
    }
}

void lcd_standby_touch_cb(lv_event_t *e)
{
}

/*****************************************LCD START*******************************/
struct LCD_START_PARAM
{
    int res;
};

void lcd_start_setup_init(void)
{
    refresh_sreen_id(LCD_START);
}

void lcd_start_touch_cb(lv_event_t *e)
{
}

/***********RTOS下对外接口***************************/
uint8_t get_active_screen_id_os(void)
{
    lvgl_msg_t msg;
    msg.type = LVGL_MSG_GET_ACTIVE_SCREEN;

    if (lvgl_msg_send_sync(&msg) == RT_EOK)
    {
        return msg.result.uint8_ret;
    }
    return 0;
}

void set_active_screen_os(enum DISP_PAGE _page)
{
    lvgl_msg_t msg;
    msg.type = LVGL_MSG_SET_ACTIVE_SCREEN;
    msg.data.set_screen.page = _page;
    rt_err_t result = lvgl_msg_send(&msg);
    if (result != RT_EOK)
    {
        rt_kprintf("set_active_screen_os: Message send failed, error: %d\n", result);
    }
}

int lcd_debug_get_current_focus_index_os(void)
{
    lvgl_msg_t msg;
    msg.type = LVGL_MSG_DEBUG_GET_FOCUS;

    if (lvgl_msg_send_sync(&msg) == RT_EOK)
    {
        return msg.result.int_ret;
    }
    return 0;
}

void lcd_debug_choose_focus_obj_os(enum LCD_DEBUG_ITEM_INDEX _index)
{
    lvgl_msg_t msg;
    msg.type = LVGL_MSG_DEBUG_CHOOSE_FOCUS;
    msg.data.debug_focus.index = _index;

    lvgl_msg_send(&msg);
}

void lcd_debug_set_temperature_os(enum LCD_DEBUG_ITEM_INDEX _index, int _num)
{
    lvgl_msg_t msg;
    msg.type = LVGL_MSG_DEBUG_SET_TEMPERATURE;
    msg.data.debug_temp.index = _index;
    msg.data.debug_temp.value = _num;

    lvgl_msg_send(&msg);
}

void lcd_debug_set_24v_output_os(enum LCD_DEBUG_ITEM_INDEX _index, int _num)
{
    lvgl_msg_t msg;
    msg.type = LVGL_MSG_DEBUG_SET_24V_OUTPUT;
    msg.data.debug_24v.index = _index;
    msg.data.debug_24v.value = _num;

    lvgl_msg_send(&msg);
}

void lcd_debug_set_signal_os(uint32_t _maskbit)
{
    lvgl_msg_t msg;
    msg.type = LVGL_MSG_DEBUG_SET_SIGNAL;
    msg.data.debug_signal.maskbit = _maskbit;

    lvgl_msg_send(&msg);
}

void lcd_debug_clear_signal_os(uint32_t _maskbit)
{
    lvgl_msg_t msg;
    msg.type = LVGL_MSG_DEBUG_CLEAR_SIGNAL;
    msg.data.debug_signal.maskbit = _maskbit;

    lvgl_msg_send(&msg);
}

void lcd_debug_set_coil_os(uint32_t _maskbit)
{
    lvgl_msg_t msg;
    msg.type = LVGL_MSG_DEBUG_SET_COIL;
    msg.data.debug_coil.maskbit = _maskbit;

    lvgl_msg_send(&msg);
}

void lcd_debug_clear_coil_os(uint32_t _maskbit)
{
    lvgl_msg_t msg;
    msg.type = LVGL_MSG_DEBUG_CLEAR_COIL;
    msg.data.debug_coil.maskbit = _maskbit;

    lvgl_msg_send(&msg);
}

void lcd_developer_change_label_txt_os(enum LCD_DEVELOPER_ITEM_INDEX _index, char *_string)
{
    lvgl_msg_t msg;
    msg.type = LVGL_MSG_DEVELOPER_CHANGE_LABEL;
    msg.data.developer_label.index = _index;
    strncpy(msg.data.developer_label.string, _string, sizeof(msg.data.developer_label.string) - 1);
    msg.data.developer_label.string[sizeof(msg.data.developer_label.string) - 1] = '\0';

    lvgl_msg_send(&msg);
}

void lcd_parameter_choose_param_value_os(int8_t _index)
{
    lvgl_msg_t msg;
    msg.type = LVGL_MSG_PARAMETER_CHOOSE_VALUE;
    msg.data.param_choose.index = _index;

    lvgl_msg_send(&msg);
}

void lcd_parameter_change_param_item_os(uint8_t _index, char *_string)
{
    lvgl_msg_t msg;
    msg.type = LVGL_MSG_PARAMETER_CHANGE_PARAM;
    msg.data.param_change.index = _index;
    strncpy(msg.data.param_change.string, _string, sizeof(msg.data.param_change.string) - 1);
    msg.data.param_change.string[sizeof(msg.data.param_change.string) - 1] = '\0';

    lvgl_msg_send(&msg);
}

void lcd_parameter_change_value_item_os(uint8_t _index, char *_string)
{
    lvgl_msg_t msg;
    msg.type = LVGL_MSG_PARAMETER_CHANGE_VALUE;
    msg.data.param_change.index = _index;
    strncpy(msg.data.param_change.string, _string, sizeof(msg.data.param_change.string) - 1);
    msg.data.param_change.string[sizeof(msg.data.param_change.string) - 1] = '\0';

    lvgl_msg_send(&msg);
}

void lcd_run_choose_workmode_os(enum LCD_RUN_WORK_MODE _workmode)
{
    LV_ENTER();
    // 选择工作模式，依据 _workmode
    lcd_run_choose_workmode(_workmode); // 调用实际的函数
    LV_QUIT();
}

void lcd_run_choose_focus_obj_os(enum LCD_RUN_ITEM_INDEX _index)
{
    LV_ENTER();
    // 选择调试焦点项，依据 _maskbit
    lcd_run_choose_focus_obj(_index); // 调用实际的函数
    LV_QUIT();
}

void lcd_run_add_focus_os(enum LCD_RUN_ITEM_BITMASK _maskbit)
{
    LV_ENTER();
    // 添加调试焦点项，依据 _maskbit
    lcd_run_add_focus(_maskbit); // 调用实际的函数
    LV_QUIT();
}

void lcd_run_clear_focus_os(enum LCD_RUN_ITEM_BITMASK _maskbit)
{
    LV_ENTER();
    // 清除调试焦点项，依据 _maskbit
    lcd_run_clear_focus(_maskbit); // 调用实际的函数
    LV_QUIT();
}

uint16_t lcd_run_get_focusbit_os(void)
{
    LV_ENTER();
    // 获取当前调试焦点的位掩码
    uint16_t focus_bit = lcd_run_get_focus_maskbit(); // 调用实际的函数
    LV_QUIT();
    return focus_bit;
}

void lcd_run_set_wash_water_level_os(enum LCD_RUN_WATER_LEVEL _level)
{
    LV_ENTER();
    // 设置洗涤水位，依据 _level
    lcd_run_set_wash_water_level(_level); // 调用实际的函数
    LV_QUIT();
}

void lcd_run_set_rinse_water_level_os(enum LCD_RUN_WATER_LEVEL _level)
{
    LV_ENTER();
    // 设置漂洗水位，依据 _level
    lcd_run_set_rinse_water_level(_level); // 调用实际的函数
    LV_QUIT();
}

void lcd_run_set_wash_preset_temperature_os(int16_t _temperature)
{
    LV_ENTER();
    // 设置洗涤预设温度，依据 _temperature
    lcd_run_set_wash_preset_temperature(_temperature); // 调用实际的函数
    LV_QUIT();
}

void lcd_run_set_wash_actual_temperature_os(int16_t _temperature)
{
    LV_ENTER();
    // 设置洗涤实际温度，依据 _temperature
    lcd_run_set_wash_actual_temperature(_temperature); // 调用实际的函数
    LV_QUIT();
}

void lcd_run_set_rinse_preset_temperature_os(int16_t _temperature)
{
    LV_ENTER();
    // 设置漂洗预设温度，依据 _temperature
    lcd_run_set_rinse_preset_temperature(_temperature); // 调用实际的函数
    LV_QUIT();
}

void lcd_run_set_rinse_actual_temperature_os(int16_t _temperature)
{
    LV_ENTER();
    // 设置漂洗实际温度，依据 _temperature
    lcd_run_set_rinse_actual_temperature(_temperature); // 调用实际的函数
    LV_QUIT();
}

void lcd_run_set_progress_os(uint8_t _num)
{
    LV_ENTER();
    // 设置进度，依据 _num
    lcd_run_set_progress(_num); // 调用实际的函数
    LV_QUIT();
}

void lcd_run_show_warning_os(char *_string)
{
    LV_ENTER();
    // 显示警告信息，依据 _string
    lcd_run_show_warning(_string); // 调用实际的函数
    LV_QUIT();
}

void lcd_run_hide_warning_os(void)
{
    LV_ENTER();
    // 隐藏警告信息
    lcd_run_hide_warning(); // 调用实际的函数
    LV_QUIT();
}

void lcd_run_setdoor_state_os(uint8_t _onoff)
{
    LV_ENTER();

    lcd_run_setdoor_state(_onoff); // 调用实际的函数
    LV_QUIT();
}
void lcd_setting_choose_param_os(enum LCD_SETTING_SUB_PARAM_INDEX _typeindex)
{
    LV_ENTER();
    lcd_setting_choose_param(_typeindex); // 调用实际的函数
    LV_QUIT();
}
void lcd_setting_change_current_value_os(char *_string)
{
    LV_ENTER();
    lcd_setting_change_current_value(_string); // 调用实际的函数
    LV_QUIT();
}
void lcd_setting_bind_os(void)
{
    LV_ENTER();
    lcd_setting_bind(); // 调用实际的函数
    LV_QUIT();
}
void lcd_settting_change_param_string_os(enum LCD_SETTING_SUB_PARAM_INDEX _index, char *_string)
{
    LV_ENTER();
    lcd_settting_change_param_string(_index, _string); // 调用实际的函数
    LV_QUIT();
}

void lcd_standby_set_roller_index_os(enum LCD_STANDBY_ROLLER_INDEX _index)
{
    LV_ENTER();
    // 设置待机时的滚筒索引，依据 _index
    lcd_standby_set_roller_index(_index); // 调用实际的函数
    LV_QUIT();
}

uint8_t lcd_standby_get_roller_index_os(void)
{
    LV_ENTER();
    // 获取当前待机时的滚筒索引
    uint8_t roller_index = lcd_standby_get_roller_index(); // 调用实际的函数
    LV_QUIT();
    return roller_index;
}

lv_ui guider_ui;
void ui_init(void)
{
    setup_ui(&guider_ui);
    events_init(&guider_ui);
    custom_init(&guider_ui);
}

/***********************Shell命令实现*****************************/

/**
 * @brief Shell命令：切换LVGL页面
 * @param argc 参数个数
 * @param argv 参数数组
 */
static void cmd_switch_page(int argc, char **argv)
{
    if (argc != 2)
    {
        rt_kprintf("Usage: switch_page <page_id>\n");
        rt_kprintf("Page IDs:\n");
        rt_kprintf("  0 - LCD_START\n");
        rt_kprintf("  1 - LCD_STANDBY\n");
        rt_kprintf("  2 - LCD_RUN\n");
        rt_kprintf("  3 - LCD_SETTING\n");
        rt_kprintf("  4 - LCD_DEBUG\n");
        rt_kprintf("  5 - LCD_PARAMETER\n");
        rt_kprintf("  6 - LCD_DEVELOPER\n");
        return;
    }

    int page_id = atoi(argv[1]);
    if (page_id < 0 || page_id >= PAGE_NUM)
    {
        rt_kprintf("Invalid page ID. Valid range: 0-%d\n", PAGE_NUM - 1);
        return;
    }

    rt_kprintf("Switching to page %d...\n", page_id);
    set_active_screen_os((enum DISP_PAGE)page_id);
}
MSH_CMD_EXPORT(cmd_switch_page, Switch LVGL page);

/**
 * @brief Shell命令：获取当前LVGL页面ID
 * @param argc 参数个数
 * @param argv 参数数组
 */
static void cmd_get_page(int argc, char **argv)
{
    uint8_t current_page = get_active_screen_id_os();
    rt_kprintf("Current page ID: %d\n", current_page);

    // 显示页面名称
    const char *page_names[] = {
        "LCD_START",
        "LCD_STANDBY",
        "LCD_RUN",
        "LCD_SETTING",
        "LCD_DEBUG",
        "LCD_PARAMETER",
        "LCD_DEVELOPER"};

    if (current_page < PAGE_NUM)
    {
        rt_kprintf("Current page name: %s\n", page_names[current_page]);
    }
    else
    {
        rt_kprintf("Unknown page\n");
    }
}
MSH_CMD_EXPORT(cmd_get_page, Get current LVGL page ID);

/**
 * @brief Shell命令：列出所有可用页面
 * @param argc 参数个数
 * @param argv 参数数组
 */
static void cmd_list_pages(int argc, char **argv)
{
    rt_kprintf("Available LVGL pages:\n");
    rt_kprintf("  0 - LCD_START\n");
    rt_kprintf("  1 - LCD_STANDBY\n");
    rt_kprintf("  2 - LCD_RUN\n");
    rt_kprintf("  3 - LCD_SETTING\n");
    rt_kprintf("  4 - LCD_DEBUG\n");
    rt_kprintf("  5 - LCD_PARAMETER\n");
    rt_kprintf("  6 - LCD_DEVELOPER\n");
    rt_kprintf("Total pages: %d\n", PAGE_NUM);
}
MSH_CMD_EXPORT(cmd_list_pages, List all available LVGL pages);
