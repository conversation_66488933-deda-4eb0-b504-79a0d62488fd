Import('RTT_ROOT')
Import('rtconfig')
from building import *
import os

cwd = GetCurrentDir()

# 递归搜索所有子目录中的源文件和头文件，排除generated/images文件夹
def getAllFiles(root_dir):
    src_files = []
    header_dirs = set()  # 使用set避免重复目录
    
    for root, dirs, files in os.walk(root_dir):
        # 检查当前路径是否是或包含generated/images
        if 'images' in root:
            continue  # 跳过此目录
            
        for file in files:
            # 收集源文件
            if file.endswith(('.c', '.cpp')):
                src_files.append(os.path.join(root, file))
            # 收集头文件所在目录
            elif file.endswith(('.h', '.hpp')):
                header_dirs.add(root)
    
    return src_files, list(header_dirs)

# 获取所有源文件和头文件目录
src_files, header_dirs = getAllFiles(cwd)

# 设置包含路径，确保所有头文件可以被找到
CPPPATH = header_dirs

CFLAGS = ' -c -ffunction-sections'

group = DefineGroup('lcd_6.2_style1', src_files, depend = [''], CPPPATH = CPPPATH, CFLAGS=CFLAGS)

Return('group')