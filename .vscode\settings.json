{
	"terminal.integrated.profiles.windows": {
		"PowerShell": {
			"source": "PowerShell",
			"icon": "terminal-powershell",
			"args": ["-NoExit", "-Command", "cmd /c ${workspaceFolder}/win_cmd.bat"],
		},
		"Command Prompt": {
			"path": [
				"${env:windir}\\Sysnative\\cmd.exe",
				"${env:windir}\\System32\\cmd.exe"
			],
			"icon": "terminal-cmd",
			"args": ["/K ${workspaceFolder}/win_cmd.bat"],
		},
		"Git Bash": {
			"source": "Git Bash",
			"args": ["-c \"source ${workspaceFolder}/tools/onestep.sh\""],
		},
	},
	"files.associations": {
		"*.s": "a51",
		"*.cjson": "jsonc",
		"*.wxss": "css",
		"*.wxs": "javascript",
		"typeindex": "cpp",
		"typeinfo": "cpp",
		"*.inc": "cpp",
		"user_api.h": "c",
		"stdio.h": "c",
		"guider_customer_fonts.h": "c",
		"aic_ui.h": "c",
		"mpp_ge.h": "c",
		"cstdio": "c",
		"of.h": "c",
		"list": "c",
		"functional": "c",
		"lvgl_msg.h": "c",
		"gui_guider.h": "c",
		"widgets_init.h": "c",
		"mtd_nor.h": "c",
		"partition_table.h": "c",
		"qrcode_data_manager.h": "c",
		"kconfig": "c",
		"*.tcc": "c",
		"memory": "c",
		"rope": "c",
		"istream": "c",
		"sstream": "c",
		"regex": "c",
		"tuple": "c",
		"valarray": "c",
		"array": "c",
		"hash_map": "c",
		"hash_set": "c",
		"bitset": "c",
		"string_view": "c",
		"slist": "c",
		"initializer_list": "c",
		"ranges": "c",
		"span": "c",
		"utility": "c",
		"cstring": "c",
		"variant": "c"
	},
	"idf.flashType": "JTAG",
	"cmake.sourceDirectory": "E:/gitshare/dishwasher/luban-lite_D12/packages/third-party/cherryusb"
}
