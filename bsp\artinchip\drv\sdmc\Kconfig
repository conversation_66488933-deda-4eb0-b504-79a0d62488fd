#-----------------------------
# SDMC devices local parameter
#-----------------------------

config AIC_SDMC_IRQ_MODE
    bool "Enable the interrupt of SDMC"
    default n
    depends on AIC_USING_SDMC0 || AIC_USING_SDMC1 || AIC_USING_SDMC2

# SDMC0 parameter

menu "SDMC0 Parameter"
    depends on AIC_USING_SDMC0

choice
    prompt "Select SDMC0 BUSWIDTH"
    default AIC_SDMC0_BUSWIDTH4
    config AIC_SDMC0_BUSWIDTH1
        bool "sdmc 1-bit mode"
    config AIC_SDMC0_BUSWIDTH4
        bool "sdmc 4-bit mode"
    config AIC_SDMC0_BUSWIDTH8
        bool "sdmc 8-bit mode"
endchoice

    config AIC_SDMC0_DDR_MODE
        bool "Using DDR mode"
        default n

    config AIC_SDMC0_DRV_PHASE
        int "SDMC0 driver phase"
        range 0 3
        default 3

    config AIC_SDMC0_SMP_PHASE
        int "SDMC0 sample phase"
        range 0 3
        default 0

    config AIC_SDMC0_CLK_FREQ
        int "SDMC0 CMU clock frequency"
        range 31500000 200000000
        default 100000000

endmenu

# SDMC1 parameter

menu "SDMC1 Parameter"
    depends on AIC_USING_SDMC1

choice
    prompt "Select SDMC1 BUSWIDTH"
    default AIC_SDMC1_BUSWIDTH4
    config AIC_SDMC1_BUSWIDTH1
        bool "sdmc 1-bit mode"
    config AIC_SDMC1_BUSWIDTH4
        bool "sdmc 4-bit mode"
endchoice

    config AIC_SD_USING_HOTPLUG
        bool "Using SDcard hotplug detection"
        default n

    config AIC_SDMC1_IS_SDIO
        bool "SDMC1 connect to a SDIO device"
        select AIC_SDMC_IRQ_MODE
        default n

    config AIC_SDMC1_DRV_PHASE
        int "SDMC1 driver phase"
        range 0 3
        default 3

    config AIC_SDMC1_SMP_PHASE
        int "SDMC1 sample phase"
        range 0 3
        default 0

    config AIC_SDMC1_CLK_FREQ
        int "SDMC1 CMU clock frequency"
        range 31500000 200000000
        default 100000000

endmenu

# SDMC2 parameter

menu "SDMC2 Parameter"
    depends on AIC_USING_SDMC2

choice
    prompt "Select SDMC2 BUSWIDTH"
    default AIC_SDMC2_BUSWIDTH4
    config AIC_SDMC2_BUSWIDTH1
        bool "sdmc 1-bit mode"
    config AIC_SDMC2_BUSWIDTH4
        bool "sdmc 4-bit mode"
endchoice

    config AIC_SDMC2_IS_SDIO
        bool "SDMC2 connect to a SDIO device"
        select AIC_SDMC_IRQ_MODE
        default n

    config AIC_SDMC2_DRV_PHASE
        int "SDMC2 driver phase"
        range 0 3
        default 3

    config AIC_SDMC2_SMP_PHASE
        int "SDMC2 sample phase"
        range 0 3
        default 0

    config AIC_SDMC2_CLK_FREQ
        int "SDMC2 CMU clock frequency"
        range 31500000 200000000
        default 100000000

endmenu

# common parameter

