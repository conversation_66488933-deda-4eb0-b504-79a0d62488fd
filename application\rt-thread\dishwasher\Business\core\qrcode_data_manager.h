/*
 * Copyright (c) 2024, ArtInChip Technology Co., Ltd
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * QR Code Data Manager - MTD-based implementation
 * Authors: <AUTHORS>
 */

#ifndef __QRCODE_DATA_MANAGER_H__
#define __QRCODE_DATA_MANAGER_H__

#include <rtthread.h>
#include <stdint.h>
#include <stdbool.h>
#include "../qrcode_config.h"

#ifdef __cplusplus
extern "C" {
#endif

#if QRCODE_DEBUG_ENABLE
#define QRCODE_DEBUG(fmt, ...) rt_kprintf("[QRCODE] " fmt "\n", ##__VA_ARGS__)
#else
#define QRCODE_DEBUG(fmt, ...)
#endif

/* Data structure for storage */
struct qrcode_data_header {
    uint32_t magic;     /* Magic number for validation */
    uint32_t crc32;     /* CRC32 checksum */
    uint32_t data_len;  /* Length of valid data */
    uint32_t reserved;  /* Reserved for future use */
};

#define QRCODE_DATA_MAGIC 0x51524344  /* "QRCD" */

/* API Functions */

/**
 * @brief Initialize QR code data manager
 * @return RT_EOK on success, error code on failure
 */
rt_err_t qrcode_data_manager_init(void);

/**
 * @brief Deinitialize QR code data manager
 * @return RT_EOK on success, error code on failure
 */
rt_err_t qrcode_data_manager_deinit(void);

/**
 * @brief Save a string value with given key
 * @param key The key name
 * @param value The string value to save
 * @return RT_EOK on success, error code on failure
 */
rt_err_t qrcode_data_save_string(const char *key, const char *value);

/**
 * @brief Get a string value by key
 * @param key The key name
 * @param buffer Buffer to store the value
 * @param buffer_size Size of the buffer
 * @return RT_EOK on success, error code on failure
 */
rt_err_t qrcode_data_get_string(const char *key, char *buffer, size_t buffer_size);

/**
 * @brief Delete a string value by key
 * @param key The key name
 * @return RT_EOK on success, error code on failure
 */
rt_err_t qrcode_data_delete_string(const char *key);

/**
 * @brief Save device information
 * @param device_id Device ID
 * @param device_sn Device serial number
 * @param model Device model (optional, can be NULL)
 * @param version Firmware version (optional, can be NULL)
 * @return RT_EOK on success, error code on failure
 */
rt_err_t qrcode_data_save_device_info(const char *device_id, const char *device_sn, 
                                      const char *model, const char *version);

/**
 * @brief Save WiFi configuration
 * @param ssid WiFi SSID
 * @param password WiFi password
 * @return RT_EOK on success, error code on failure
 */
rt_err_t qrcode_data_save_wifi_info(const char *ssid, const char *password);

/**
 * @brief Generate QR code string in JSON format
 * @param buffer Buffer to store the JSON string
 * @param buffer_size Size of the buffer
 * @return RT_EOK on success, error code on failure
 */
rt_err_t qrcode_data_generate_qr_string(char *buffer, size_t buffer_size);

/**
 * @brief Clear all stored data
 * @return RT_EOK on success, error code on failure
 */
rt_err_t qrcode_data_clear_all(void);

/**
 * @brief Check if data manager is initialized
 * @return true if initialized, false otherwise
 */
bool qrcode_data_is_initialized(void);

#ifdef __cplusplus
}
#endif

#endif /* __QRCODE_DATA_MANAGER_H__ */