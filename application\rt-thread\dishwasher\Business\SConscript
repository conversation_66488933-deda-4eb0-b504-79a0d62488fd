Import('RTT_ROOT')
Import('rtconfig')
from building import *

cwd     = GetCurrentDir()
src     = Glob('*.c')
# Add core directory sources (exclude if already included by subdirectory)
if not os.path.exists(os.path.join(cwd, 'core', 'SConscript')):
    src += Glob('core/*.c')
CPPPATH = [cwd, cwd + '/core']

CFLAGS = ' -c -ffunction-sections'

group   = DefineGroup('Business', src, depend = [''], CPPPATH = CPPPATH, CFLAGS=CFLAGS)

list = os.listdir(cwd)
for item in list:
    if os.path.isfile(os.path.join(cwd, item, 'SConscript')):
        group = group + SConscript(os.path.join(item, 'SConscript'))

Return('group')
