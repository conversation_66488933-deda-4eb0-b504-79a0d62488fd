/*
 * Copyright (c) 2022, Artinchip Technology Co., Ltd
 *
 * SPDX-License-Identifier: Apache-2.0
 */

#ifndef __AIC_RESET_ID_H__
#define __AIC_RESET_ID_H__

#ifdef __cplusplus
extern "C" {
#endif

#define RESET_DMA       0
#define RESET_CE        1
#define RESET_USBD      2
#define RESET_USBH0     3
#define RESET_USBPHY0   4
#define RESET_GMAC0     5
#define RESET_XSPI      6
#define RESET_QSPI0     7
#define RESET_QSPI1     8
#define RESET_QSPI2     9
#define RESET_QSPI3     10
#define RESET_SDMMC0    11
#define RESET_SDMMC1    12
#define RESET_SYSCFG    13
#define RESET_RTC       14
#define RESET_SPIENC    15
#define RESET_I2S0      16
#define RESET_CODEC     17
#define RESET_RGB       18
#define RESET_LVDS      19
#define RESET_MIPIDSI   20
#define RESET_DE        21
#define RESET_GE        22
#define RESET_VE        23
#define RESET_WDT       24
#define RESET_SID       25
#define RESET_GTC       26
#define RESET_GPIO      27
#define RESET_UART0     28
#define RESET_UART1     29
#define RESET_UART2     30
#define RESET_UART3     31
#define RESET_UART4     32
#define RESET_UART5     33
#define RESET_UART6     34
#define RESET_UART7     35
#define RESET_I2C0      36
#define RESET_I2C1      37
#define RESET_I2C2      38
#define RESET_CAN0      39
#define RESET_CAN1      40
#define RESET_PWM       41
#define RESET_ADCIM     42
#define RESET_GPAI      43
#define RESET_RTP       44
#define RESET_TSEN      45
#define RESET_CIR       46
#define RESET_DVP       47
#define RESET_MTOP      48
#define RESET_PBUS      49
#define RESET_PSADC     50
#define RESET_PWMCS     51
#define RESET_PWMCS_SDFM    52
#define RESET_NUMBER    53

#ifdef __cplusplus
}
#endif

#endif /* __AIC_RESET_ID_H__ */
