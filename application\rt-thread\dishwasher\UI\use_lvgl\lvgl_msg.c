#include <rtthread.h>
#include <stdio.h>
#include "lvgl_msg.h"
#include "user_api.h"

static rt_mq_t lvgl_mq; // 消息队列

rt_err_t lvgl_msg_init(void)
{
    // 创建消息队列
    lvgl_mq = rt_mq_create("lvgl_mq", sizeof(lvgl_msg_t), LVGL_MQ_SIZE, RT_IPC_FLAG_FIFO);
    if (lvgl_mq == RT_NULL)
    {
        rt_kprintf("lvgl_msg_init: Failed to create message queue\n");
        return RT_ERROR;
    }

    rt_kprintf("lvgl_msg_init: Message queue created successfully\n");
    return RT_EOK;
}

// 异步发送消息
rt_err_t lvgl_msg_send(lvgl_msg_t *msg)
{
    if (lvgl_mq == RT_NULL || msg == RT_NULL)
    {
        rt_kprintf("lvgl_msg_send: Invalid parameters (mq=%p, msg=%p)\n", lvgl_mq, msg);
        return RT_ERROR;
    }
    msg->response_sem = RT_NULL; // 异步消息不需要信号量
    // 检查消息队列状态
    rt_err_t result = rt_mq_send(lvgl_mq, msg, sizeof(lvgl_msg_t));
    if (result != RT_EOK)
    {
        rt_kprintf("lvgl_msg_send: Send failed! Queue full or other error\n");
    }

    return result;
}

// 同步发送消息（等待返回值）
rt_err_t lvgl_msg_send_sync(lvgl_msg_t *msg)
{
    rt_err_t result;

    if (lvgl_mq == RT_NULL || msg == RT_NULL)
    {
        return -RT_ERROR;
    }
    // 创建信号量用于同步
    msg->response_sem = rt_sem_create("lvgl_sync", 0, RT_IPC_FLAG_FIFO);
    if (msg->response_sem == RT_NULL)
    {
        return -RT_ERROR;
    }

    // 发送消息
    result = rt_mq_send(lvgl_mq, msg, sizeof(lvgl_msg_t));
    if (result != RT_EOK)
    {
        rt_sem_delete(msg->response_sem);
        return result;
    }

    // 等待处理完成
    result = rt_sem_take(msg->response_sem, RT_WAITING_FOREVER);
    rt_sem_delete(msg->response_sem);

    return result;
}

// 消息处理任务
void lvgl_msg_process_task(void)
{
    lvgl_msg_t msg;
    rt_err_t result;

    if (lvgl_mq == RT_NULL)
    {
        rt_kprintf("lvgl_msg_process_task: Invalid parameters\n");
        return;
    }
    // 接收消息（阻塞等待）
    result = rt_mq_recv(lvgl_mq, &msg, sizeof(lvgl_msg_t), RT_WAITING_NO);

    if (result != RT_EOK)
    {
        
        return; // 接收失败，直接返回
    }

    // 处理消息
    switch (msg.type)
    {
    case LVGL_MSG_SET_ACTIVE_SCREEN:
        rt_kprintf("lvgl_msg_process_task: LVGL_MSG_SET_ACTIVE_SCREEN\n");
        set_active_screen((enum DISP_PAGE)msg.data.set_screen.page);
        break;

    case LVGL_MSG_GET_ACTIVE_SCREEN:
        msg.result.uint8_ret = get_active_screen_id();
        break;

    case LVGL_MSG_DEBUG_SET_TEMPERATURE:
        lcd_debug_set_temperature((enum LCD_DEBUG_ITEM_INDEX)msg.data.debug_temp.index, msg.data.debug_temp.value);
        break;

    case LVGL_MSG_DEBUG_SET_24V_OUTPUT:
        lcd_debug_set_24v_output((enum LCD_DEBUG_ITEM_INDEX)msg.data.debug_24v.index, msg.data.debug_24v.value);
        break;

    case LVGL_MSG_DEBUG_SET_SIGNAL:
        lcd_debug_set_signal(msg.data.debug_signal.maskbit);
        break;

    case LVGL_MSG_DEBUG_CLEAR_SIGNAL:
        lcd_debug_clear_signal(msg.data.debug_signal.maskbit);
        break;

    case LVGL_MSG_DEBUG_SET_COIL:
        lcd_debug_set_coil(msg.data.debug_coil.maskbit);
        break;

    case LVGL_MSG_DEBUG_CLEAR_COIL:
        lcd_debug_clear_coil(msg.data.debug_coil.maskbit);
        break;

    case LVGL_MSG_DEBUG_CHOOSE_FOCUS:
        lcd_debug_choose_focus_obj((enum LCD_DEBUG_ITEM_INDEX)msg.data.debug_focus.index);
        break;

    case LVGL_MSG_DEBUG_GET_FOCUS:
        msg.result.int_ret = lcd_debug_get_current_focus_index();
        break;

    case LVGL_MSG_DEVELOPER_CHANGE_LABEL:
        lcd_developer_change_label_txt((enum LCD_DEVELOPER_ITEM_INDEX)msg.data.developer_label.index,
                                       msg.data.developer_label.string);
        break;

    case LVGL_MSG_PARAMETER_CHOOSE_VALUE:
        lcd_parameter_choose_param_value(msg.data.param_choose.index);
        break;

    case LVGL_MSG_PARAMETER_CHANGE_PARAM:
        lcd_parameter_change_param_item(msg.data.param_change.index, msg.data.param_change.string);
        break;

    case LVGL_MSG_PARAMETER_CHANGE_VALUE:
        lcd_parameter_change_value_item(msg.data.param_change.index, msg.data.param_change.string);
        break;

    // 添加更多消息处理...
    default:
        rt_kprintf("Unknown message type: %d\n", msg.type);
        break;
    }

    // 如果是同步消息，释放信号量
    if (msg.response_sem != RT_NULL)
    {
        rt_sem_release(msg.response_sem);
    }
}