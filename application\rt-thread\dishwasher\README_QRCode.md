# QR码数据管理器 - MTD版本

本文档介绍如何在D12-HMI洗碗机项目中使用基于MTD接口的二维码数据管理器来保存字符串并生成二维码。

## 功能特性

- 基于MTD接口的持久化存储（替代FlashDB）
- 支持设备信息、WiFi配置、服务器URL等多种数据类型
- 自动生成JSON格式的二维码字符串
- 与LVGL UI集成，支持动态二维码更新
- 提供完整的API接口和命令行工具
- 数据完整性校验（CRC32）
- 线程安全的操作

## 文件结构

```
dishwasher/
├── Business/core/
│   ├── qrcode_data_manager.c    # 核心实现文件
│   └── qrcode_data_manager.h    # 头文件
├── qrcode_config.h              # 配置文件
├── qrcode_demo.c                # 演示程序和命令行接口
├── qrcode_test.c                # 测试程序
├── UI/use_lvgl/.../setup_scr_lcd_standby.c  # UI集成
└── README_QRCode.md             # 本文档
```

## 存储配置

### MTD设备配置

数据存储在NOR Flash设备上：
- **设备名称**: `norflash0`
- **存储偏移**: `640KB` (0x100000)
- **存储大小**: `4KB` (0x1000)
- **数据格式**: Header + Key-Value pairs

### 数据结构

```c
struct qrcode_data_header {
    uint32_t magic;     // 魔数: 0x51524344 ("QRCD")
    uint32_t crc32;     // CRC32校验和
    uint32_t data_len;  // 有效数据长度
    uint32_t reserved;  // 保留字段
};
```

## API接口

### 初始化和清理

```c
// 初始化QR码数据管理器
rt_err_t qrcode_data_manager_init(void);

// 反初始化
rt_err_t qrcode_data_manager_deinit(void);

// 检查是否已初始化
bool qrcode_data_is_initialized(void);
```

### 数据操作

```c
// 保存字符串
rt_err_t qrcode_data_save_string(const char *key, const char *value);

// 读取字符串
rt_err_t qrcode_data_get_string(const char *key, char *buffer, size_t buffer_size);

// 删除字符串
rt_err_t qrcode_data_delete_string(const char *key);

// 清空所有数据
rt_err_t qrcode_data_clear_all(void);
```

### 便捷接口

```c
// 保存设备信息
rt_err_t qrcode_data_save_device_info(const char *device_id, const char *device_sn, 
                                      const char *model, const char *version);

// 保存WiFi信息
rt_err_t qrcode_data_save_wifi_info(const char *ssid, const char *password);

// 生成QR码字符串
rt_err_t qrcode_data_generate_qr_string(char *buffer, size_t buffer_size);
```

## 命令行接口

系统提供了完整的命令行接口：

### 基本操作

```bash
# 初始化数据管理器
qrcode_demo init

# 清空所有数据
qrcode_demo clear

# 设置键值对
qrcode_demo set device_id DW-2025-001
qrcode_demo set wifi_ssid MyWiFi

# 读取值
qrcode_demo get device_id
qrcode_demo get wifi_ssid

# 删除键
qrcode_demo del device_id
```

### 便捷操作

```bash
# 设置设备信息
qrcode_demo device DW-2025-001 SN123456789 D12-HMI v2.0.0

# 设置WiFi信息
qrcode_demo wifi MyHomeWiFi password123

# 生成QR码字符串
qrcode_demo generate

# 刷新UI中的QR码
qrcode_demo refresh

# 查看状态
qrcode_demo status
```

### 测试命令

```bash
# 运行功能测试
qrcode_test
```

## UI集成

### 自动刷新

系统会在以下情况自动刷新UI中的QR码：
- 数据更新后
- 系统启动时
- 手动调用刷新命令

### 手动刷新

```c
#include "gui_guider.h"
extern lv_ui guider_ui;
extern void refresh_qrcode_content(lv_ui *ui);

// 刷新QR码显示
refresh_qrcode_content(&guider_ui);
```

## 配置选项

在 `qrcode_config.h` 中可以配置：

```c
#define QRCODE_MAX_STRING_LEN       128     // 最大字符串长度
#define QRCODE_JSON_BUFFER_SIZE     512     // JSON缓冲区大小
#define QRCODE_DATA_SIZE            (4*1024) // 存储区域大小
#define QRCODE_MTD_DEVICE_NAME      "norflash0" // MTD设备名
#define QRCODE_DATA_OFFSET          (640*1024)  // 存储偏移
#define QRCODE_DEBUG_ENABLE         1       // 调试输出
```

## 预定义键名

```c
#define QRCODE_KEY_DEVICE_ID        "device_id"
#define QRCODE_KEY_DEVICE_SN        "device_sn"
#define QRCODE_KEY_WIFI_SSID        "wifi_ssid"
#define QRCODE_KEY_WIFI_PASSWORD    "wifi_password"
#define QRCODE_KEY_SERVER_URL       "server_url"
#define QRCODE_KEY_CUSTOM_INFO      "custom_info"
```

## 使用示例

### 基本使用

```c
#include "Business/core/qrcode_data_manager.h"

int main(void)
{
    // 初始化
    rt_err_t result = qrcode_data_manager_init();
    if (result != RT_EOK) {
        rt_kprintf("Failed to initialize QR data manager\n");
        return -1;
    }
    
    // 保存数据
    qrcode_data_save_string("device_id", "DW-2025-001");
    qrcode_data_save_wifi_info("MyWiFi", "password123");
    
    // 生成QR码
    char qr_string[512];
    result = qrcode_data_generate_qr_string(qr_string, sizeof(qr_string));
    if (result == RT_EOK) {
        rt_kprintf("QR Data: %s\n", qr_string);
    }
    
    return 0;
}
```

### JSON输出格式

生成的QR码字符串为JSON格式：

```json
{
    "device_id": "DW-2025-001",
    "device_sn": "SN123456789",
    "wifi_ssid": "MyWiFi",
    "wifi_password": "password123",
    "server_url": "https://api.example.com"
}
```

## 技术特性

### 数据完整性
- 使用CRC32校验确保数据完整性
- 魔数验证防止数据损坏
- 自动恢复机制

### 线程安全
- 使用互斥锁保护数据访问
- 支持多线程并发操作

### 存储效率
- 键值对格式存储
- 动态数据长度
- 最小化Flash擦写次数

### 错误处理
- 完整的错误码返回
- 调试信息输出
- 异常情况恢复

## 故障排除

### 常见问题

1. **初始化失败**
   - 检查MTD设备是否正确配置
   - 确认Flash分区设置
   - 查看调试输出信息

2. **数据读取失败**
   - 检查CRC校验
   - 确认数据格式
   - 重新初始化数据区域

3. **UI不更新**
   - 确认LVGL已启用
   - 检查UI对象是否创建
   - 手动调用刷新函数

### 调试方法

```bash
# 查看系统状态
qrcode_demo status

# 运行测试
qrcode_test

# 清空数据重新开始
qrcode_demo clear
qrcode_demo init
```

## 扩展开发

### 添加新的数据类型

1. 在 `qrcode_config.h` 中定义新的键名
2. 在 `qrcode_data_generate_qr_string` 中添加JSON生成逻辑
3. 添加相应的便捷接口函数

### 自定义存储格式

修改 `qrcode_data_generate_qr_string` 函数来自定义输出格式。

### 集成其他UI框架

参考LVGL集成方式，实现对应的刷新接口。

## 版本历史

- **v2.0.0**: MTD版本，替代FlashDB实现
- **v1.0.0**: 基于FlashDB的初始版本

## 许可证

Apache-2.0