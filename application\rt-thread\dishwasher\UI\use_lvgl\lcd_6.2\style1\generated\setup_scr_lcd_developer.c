/*
* Copyright 2025 NXP
* NXP Proprietary. This software is owned or controlled by NXP and may only be used strictly in
* accordance with the applicable license terms. By expressly accepting such terms or by downloading, installing,
* activating and/or otherwise using the software, you are agreeing that you have read, and that you agree to
* comply with and are bound by, such license terms.  If you do not agree to be bound by the applicable license
* terms, then you may not retain, install, activate or otherwise use the software.
*/

#include "lvgl.h"
#include <stdio.h>
#include "gui_guider.h"
#include "events_init.h"
#include "widgets_init.h"
#include "custom.h"
#include "user_api.h"


void setup_scr_lcd_developer(lv_ui *ui)
{
    //Write codes lcd_developer
    ui->lcd_developer = lv_obj_create(NULL);
    lv_obj_set_size(ui->lcd_developer, 960, 360);
    lv_obj_set_scrollbar_mode(ui->lcd_developer, LV_SCROLLBAR_MODE_OFF);

    //Write style for lcd_developer, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->lcd_developer, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->lcd_developer, lv_color_hex(0x282929), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->lcd_developer, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes lcd_developer_cont_1
    ui->lcd_developer_cont_1 = lv_obj_create(ui->lcd_developer);
    lv_obj_set_pos(ui->lcd_developer_cont_1, 0, 0);
    lv_obj_set_size(ui->lcd_developer_cont_1, 960, 360);
    lv_obj_set_scrollbar_mode(ui->lcd_developer_cont_1, LV_SCROLLBAR_MODE_AUTO);

    //Write style for lcd_developer_cont_1, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->lcd_developer_cont_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->lcd_developer_cont_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->lcd_developer_cont_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->lcd_developer_cont_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->lcd_developer_cont_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->lcd_developer_cont_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->lcd_developer_cont_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->lcd_developer_cont_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //The custom code of lcd_developer.
    lcd_developer_setup_init();

    //Update current screen layout.
    lv_obj_update_layout(ui->lcd_developer);

}
