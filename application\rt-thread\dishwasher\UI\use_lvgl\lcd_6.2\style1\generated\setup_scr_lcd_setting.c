/*
* Copyright 2025 NXP
* NXP Proprietary. This software is owned or controlled by NXP and may only be used strictly in
* accordance with the applicable license terms. By expressly accepting such terms or by downloading, installing,
* activating and/or otherwise using the software, you are agreeing that you have read, and that you agree to
* comply with and are bound by, such license terms.  If you do not agree to be bound by the applicable license
* terms, then you may not retain, install, activate or otherwise use the software.
*/

#include "lvgl.h"
#include <stdio.h>
#include "gui_guider.h"
#include "events_init.h"
#include "widgets_init.h"
#include "custom.h"
#include "user_api.h"
#include "aic_ui.h"


void setup_scr_lcd_setting(lv_ui *ui)
{
    //Write codes lcd_setting
    ui->lcd_setting = lv_obj_create(NULL);
    lv_obj_set_size(ui->lcd_setting, 960, 360);
    lv_obj_set_scrollbar_mode(ui->lcd_setting, LV_SCROLLBAR_MODE_OFF);

    //Write style for lcd_setting, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->lcd_setting, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->lcd_setting, lv_color_hex(0x282929), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->lcd_setting, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes lcd_setting_cont_1
    ui->lcd_setting_cont_1 = lv_obj_create(ui->lcd_setting);
    lv_obj_set_pos(ui->lcd_setting_cont_1, 399, 150);
    lv_obj_set_size(ui->lcd_setting_cont_1, 470, 120);
    lv_obj_set_scrollbar_mode(ui->lcd_setting_cont_1, LV_SCROLLBAR_MODE_OFF);
    lv_obj_add_flag(ui->lcd_setting_cont_1, LV_OBJ_FLAG_SCROLLABLE);

    //Write style for lcd_setting_cont_1, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->lcd_setting_cont_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->lcd_setting_cont_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->lcd_setting_cont_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->lcd_setting_cont_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->lcd_setting_cont_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->lcd_setting_cont_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->lcd_setting_cont_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->lcd_setting_cont_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes lcd_setting_line_1
    ui->lcd_setting_line_1 = lv_line_create(ui->lcd_setting);
    static lv_point_t lcd_setting_line_1_points[] = {{25, 0},{0, 25},{25, 50},};
    lv_line_set_points(ui->lcd_setting_line_1, lcd_setting_line_1_points, 3);
    lv_obj_set_pos(ui->lcd_setting_line_1, 366, 150);
    lv_obj_set_size(ui->lcd_setting_line_1, 33, 58);

    //Write style for lcd_setting_line_1, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_line_width(ui->lcd_setting_line_1, 5, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_line_color(ui->lcd_setting_line_1, lv_color_hex(0x757575), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_line_opa(ui->lcd_setting_line_1, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_line_rounded(ui->lcd_setting_line_1, true, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes lcd_setting_line_2
    ui->lcd_setting_line_2 = lv_line_create(ui->lcd_setting);
    static lv_point_t lcd_setting_line_2_points[] = {{0, 0},{25, 25},{0, 50},};
    lv_line_set_points(ui->lcd_setting_line_2, lcd_setting_line_2_points, 3);
    lv_obj_set_pos(ui->lcd_setting_line_2, 873, 150);
    lv_obj_set_size(ui->lcd_setting_line_2, 35, 55);

    //Write style for lcd_setting_line_2, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_line_width(ui->lcd_setting_line_2, 5, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_line_color(ui->lcd_setting_line_2, lv_color_hex(0x757575), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_line_opa(ui->lcd_setting_line_2, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_line_rounded(ui->lcd_setting_line_2, true, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes lcd_setting_list_1

    ui->lcd_setting_list_1 = lv_list_create(ui->lcd_setting);
  
    ui->lcd_setting_list_1_item0 = lv_list_add_btn(ui->lcd_setting_list_1, LVGL_PATH(temp.png), "NTC温度信号");
   
    ui->lcd_setting_list_1_item1 = lv_list_add_btn(ui->lcd_setting_list_1, LVGL_PATH(onoff.png), "开关信号");
  
    ui->lcd_setting_list_1_item2 = lv_list_add_btn(ui->lcd_setting_list_1, LVGL_PATH(current_24v.png), "24V直流输出");
   
    ui->lcd_setting_list_1_item3 = lv_list_add_btn(ui->lcd_setting_list_1, LVGL_PATH(220v.png), "220V继电器");
    lv_obj_set_pos(ui->lcd_setting_list_1, -360, -1);
    lv_obj_set_size(ui->lcd_setting_list_1, 360, 360);
    lv_obj_set_scrollbar_mode(ui->lcd_setting_list_1, LV_SCROLLBAR_MODE_AUTO);

   // Write style state: LV_STATE_DEFAULT for &style_lcd_setting_list_1_main_main_default
    static lv_style_t style_lcd_setting_list_1_main_default;
    ui_init_style(&style_lcd_setting_list_1_main_default);

    lv_style_set_pad_top(&style_lcd_setting_list_1_main_default, 5);
    lv_style_set_pad_left(&style_lcd_setting_list_1_main_default, 5);
    lv_style_set_pad_right(&style_lcd_setting_list_1_main_default, 5);
    lv_style_set_pad_bottom(&style_lcd_setting_list_1_main_default, 5);
    lv_style_set_bg_opa(&style_lcd_setting_list_1_main_default, 255);
    lv_style_set_bg_color(&style_lcd_setting_list_1_main_default, lv_color_hex(0x383838));
    lv_style_set_bg_grad_dir(&style_lcd_setting_list_1_main_default, LV_GRAD_DIR_NONE);
    lv_style_set_border_width(&style_lcd_setting_list_1_main_default, 0);
    lv_style_set_radius(&style_lcd_setting_list_1_main_default, 0);
    lv_style_set_shadow_width(&style_lcd_setting_list_1_main_default, 0);
    lv_obj_add_style(ui->lcd_setting_list_1, &style_lcd_setting_list_1_main_default, LV_PART_MAIN|LV_STATE_DEFAULT);
   
    //Write style state: LV_STATE_FOCUSED for &style_lcd_setting_list_1_main_main_focused
    static lv_style_t style_lcd_setting_list_1_main_focused;
    ui_init_style(&style_lcd_setting_list_1_main_focused);

    lv_style_set_pad_top(&style_lcd_setting_list_1_main_focused, 5);
    lv_style_set_pad_left(&style_lcd_setting_list_1_main_focused, 5);
    lv_style_set_pad_right(&style_lcd_setting_list_1_main_focused, 5);
    lv_style_set_pad_bottom(&style_lcd_setting_list_1_main_focused, 5);
    lv_style_set_bg_opa(&style_lcd_setting_list_1_main_focused, 255);
    lv_style_set_bg_color(&style_lcd_setting_list_1_main_focused, lv_color_hex(0x383838));
    lv_style_set_bg_grad_dir(&style_lcd_setting_list_1_main_focused, LV_GRAD_DIR_NONE);
    lv_style_set_border_width(&style_lcd_setting_list_1_main_focused, 1);
    lv_style_set_border_opa(&style_lcd_setting_list_1_main_focused, 255);
    lv_style_set_border_color(&style_lcd_setting_list_1_main_focused, lv_color_hex(0xe1e6ee));
    lv_style_set_border_side(&style_lcd_setting_list_1_main_focused, LV_BORDER_SIDE_FULL);
    lv_style_set_radius(&style_lcd_setting_list_1_main_focused, 3);
    lv_style_set_shadow_width(&style_lcd_setting_list_1_main_focused, 0);
    lv_obj_add_style(ui->lcd_setting_list_1, &style_lcd_setting_list_1_main_focused, LV_PART_MAIN|LV_STATE_FOCUSED);

    // //Write style state: LV_STATE_DEFAULT for &style_lcd_setting_list_1_main_scrollbar_default
    static lv_style_t style_lcd_setting_list_1_main_scrollbar_default;
    ui_init_style(&style_lcd_setting_list_1_main_scrollbar_default);

    lv_style_set_radius(&style_lcd_setting_list_1_main_scrollbar_default, 3);
    lv_style_set_bg_opa(&style_lcd_setting_list_1_main_scrollbar_default, 255);
    lv_style_set_bg_color(&style_lcd_setting_list_1_main_scrollbar_default, lv_color_hex(0xffffff));
    lv_style_set_bg_grad_dir(&style_lcd_setting_list_1_main_scrollbar_default, LV_GRAD_DIR_NONE);
    lv_obj_add_style(ui->lcd_setting_list_1, &style_lcd_setting_list_1_main_scrollbar_default, LV_PART_SCROLLBAR|LV_STATE_DEFAULT);

    //Write style state: LV_STATE_DEFAULT for &style_lcd_setting_list_1_extra_btns_main_default
    static lv_style_t style_lcd_setting_list_1_extra_btns_main_default;
    ui_init_style(&style_lcd_setting_list_1_extra_btns_main_default);

    lv_style_set_pad_top(&style_lcd_setting_list_1_extra_btns_main_default, 5);
    lv_style_set_pad_left(&style_lcd_setting_list_1_extra_btns_main_default, 5);
    lv_style_set_pad_right(&style_lcd_setting_list_1_extra_btns_main_default, 5);
    lv_style_set_pad_bottom(&style_lcd_setting_list_1_extra_btns_main_default, 5);
    lv_style_set_border_width(&style_lcd_setting_list_1_extra_btns_main_default, 0);
    lv_style_set_text_color(&style_lcd_setting_list_1_extra_btns_main_default, lv_color_hex(0xffffff));
    lv_style_set_text_font(&style_lcd_setting_list_1_extra_btns_main_default, lv_ft_info.font);
    lv_style_set_text_opa(&style_lcd_setting_list_1_extra_btns_main_default, 255);
    lv_style_set_radius(&style_lcd_setting_list_1_extra_btns_main_default, 3);
    lv_style_set_bg_opa(&style_lcd_setting_list_1_extra_btns_main_default, 255);
    lv_style_set_bg_color(&style_lcd_setting_list_1_extra_btns_main_default, lv_color_hex(0x383838));
    lv_style_set_bg_grad_dir(&style_lcd_setting_list_1_extra_btns_main_default, LV_GRAD_DIR_NONE);
    lv_obj_add_style(ui->lcd_setting_list_1_item3, &style_lcd_setting_list_1_extra_btns_main_default, LV_PART_MAIN|LV_STATE_DEFAULT); 
    lv_obj_add_style(ui->lcd_setting_list_1_item2, &style_lcd_setting_list_1_extra_btns_main_default, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_add_style(ui->lcd_setting_list_1_item1, &style_lcd_setting_list_1_extra_btns_main_default, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_add_style(ui->lcd_setting_list_1_item0, &style_lcd_setting_list_1_extra_btns_main_default, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write style state: LV_STATE_DEFAULT for &style_lcd_setting_list_1_extra_texts_main_default
    static lv_style_t style_lcd_setting_list_1_extra_texts_main_default;
    ui_init_style(&style_lcd_setting_list_1_extra_texts_main_default);

    lv_style_set_pad_top(&style_lcd_setting_list_1_extra_texts_main_default, 20);
    lv_style_set_pad_left(&style_lcd_setting_list_1_extra_texts_main_default, 0);
    lv_style_set_pad_right(&style_lcd_setting_list_1_extra_texts_main_default, 0);
    lv_style_set_pad_bottom(&style_lcd_setting_list_1_extra_texts_main_default, 0);
    lv_style_set_border_width(&style_lcd_setting_list_1_extra_texts_main_default, 1);
    lv_style_set_border_opa(&style_lcd_setting_list_1_extra_texts_main_default, 255);
    lv_style_set_border_color(&style_lcd_setting_list_1_extra_texts_main_default, lv_color_hex(0xffffff));
    lv_style_set_border_side(&style_lcd_setting_list_1_extra_texts_main_default, LV_BORDER_SIDE_BOTTOM);
    lv_style_set_text_color(&style_lcd_setting_list_1_extra_texts_main_default, lv_color_hex(0xffffff));
    lv_style_set_text_font(&style_lcd_setting_list_1_extra_texts_main_default, lv_ft_info.font);
    lv_style_set_text_opa(&style_lcd_setting_list_1_extra_texts_main_default, 255);
    lv_style_set_radius(&style_lcd_setting_list_1_extra_texts_main_default, 0);
    lv_style_set_transform_width(&style_lcd_setting_list_1_extra_texts_main_default, 0);
    lv_style_set_bg_opa(&style_lcd_setting_list_1_extra_texts_main_default, 255);
    lv_style_set_bg_color(&style_lcd_setting_list_1_extra_texts_main_default, lv_color_hex(0x383838));
    lv_style_set_bg_grad_dir(&style_lcd_setting_list_1_extra_texts_main_default, LV_GRAD_DIR_NONE);

    //The custom code of lcd_setting.
    lcd_setting_setup_init();

    //Update current screen layout.
    lv_obj_update_layout(ui->lcd_setting);

    //Init events for screen.
    events_init_lcd_setting(ui);
}
