# RT-Thread building script for bridge

import os
from building import *

Import('rtconfig')

cwd     = GetCurrentDir()
src     = Glob('*.c')
CPPPATH = [cwd, cwd + '/include']
CPPPATH = []

CPPPATH.append(cwd + '/include')
CPPPATH.append(cwd + '../../../bsp/artinchip/include/uapi')
CPPPATH.append(cwd + '../../../bsp/artinchip/include/drv_bare')

CFLAGS  = ' -c -ffunction-sections -ffreestanding '

group   = DefineGroup('Applications', src, depend = [''], CPPPATH = CPPPATH, CFLAGS=CFLAGS)

lst  = os.listdir(cwd)

for item in lst:
    ipath = '{}/SConscript'.format(item)
    if os.path.isfile(cwd + '/' + ipath) == False:
        continue
    group = group + SConscript(ipath)

Return('group')
