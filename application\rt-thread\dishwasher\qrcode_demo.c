/*
 * Copyright (c) 2024, ArtInChip Technology Co., Ltd
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * QR Code Demo - Command line interface and UI integration
 * Authors: <AUTHORS>
 */

#include <rtthread.h>
#include <string.h>
#include <stdio.h>
#include "Business/core/qrcode_data_manager.h"
#define PKG_USING_LVGL
#ifdef PKG_USING_LVGL
#include "lvgl.h"
#include "gui_guider.h"

extern lv_ui guider_ui;

/**
 * @brief Refresh QR code content in UI
 * @param ui Pointer to UI structure
 */
void refresh_qrcode_content(lv_ui *ui)
{
    char qr_string[QRCODE_JSON_BUFFER_SIZE];
    rt_err_t result;
    
    if (!ui || !ui->lcd_standby_qrcode_1) {
        rt_kprintf("UI or QR code object is NULL\n");
        return;
    }
    
    result = qrcode_data_generate_qr_string(qr_string, sizeof(qr_string));
    if (result == RT_EOK) {
        lv_qrcode_update(ui->lcd_standby_qrcode_1, qr_string, strlen(qr_string));
        rt_kprintf("QR code updated: %s\n", qr_string);
    } else {
        rt_kprintf("Failed to generate QR string: %d\n", result);
        /* Show empty QR code on error */
        lv_qrcode_update(ui->lcd_standby_qrcode_1, "", 0);
    }
}

/**
 * @brief Auto refresh QR code content using global UI
 */
void auto_refresh_qrcode(void)
{
    refresh_qrcode_content(&guider_ui);
}
#endif

/**
 * @brief QR code demo command handler
 * @param argc Argument count
 * @param argv Argument vector
 * @return 0 on success, -1 on error
 */
static int qrcode_demo_cmd(int argc, char **argv)
{
    rt_err_t result;
    char buffer[QRCODE_MAX_STRING_LEN];
    
    if (argc < 2) {
        rt_kprintf("Usage:\n");
        rt_kprintf("  qrcode_demo init                    - Initialize QR data manager\n");
        rt_kprintf("  qrcode_demo clear                   - Clear all data\n");
        rt_kprintf("  qrcode_demo set <key> <value>       - Set key-value pair\n");
        rt_kprintf("  qrcode_demo get <key>               - Get value by key\n");
        rt_kprintf("  qrcode_demo del <key>               - Delete key\n");
        rt_kprintf("  qrcode_demo device <id> <sn>        - Set device info\n");
        rt_kprintf("  qrcode_demo wifi <ssid> <password>  - Set WiFi info\n");
        rt_kprintf("  qrcode_demo generate                - Generate QR string\n");
        rt_kprintf("  qrcode_demo refresh                 - Refresh UI QR code\n");
        rt_kprintf("  qrcode_demo status                  - Show status\n");
        return -1;
    }
    
    if (strcmp(argv[1], "init") == 0) {
        result = qrcode_data_manager_init();
        if (result == RT_EOK) {
            rt_kprintf("QR data manager initialized successfully\n");
        } else {
            rt_kprintf("Failed to initialize QR data manager: %d\n", result);
        }
    }
    else if (strcmp(argv[1], "clear") == 0) {
        result = qrcode_data_clear_all();
        if (result == RT_EOK) {
            rt_kprintf("All data cleared\n");
#ifdef PKG_USING_LVGL
            auto_refresh_qrcode();
#endif
        } else {
            rt_kprintf("Failed to clear data: %d\n", result);
        }
    }
    else if (strcmp(argv[1], "set") == 0) {
        if (argc < 4) {
            rt_kprintf("Usage: qrcode_demo set <key> <value>\n");
            return -1;
        }
        
        result = qrcode_data_save_string(argv[2], argv[3]);
        if (result == RT_EOK) {
            rt_kprintf("Set %s = %s\n", argv[2], argv[3]);
#ifdef PKG_USING_LVGL
            auto_refresh_qrcode();
#endif
        } else {
            rt_kprintf("Failed to set %s: %d\n", argv[2], result);
        }
    }
    else if (strcmp(argv[1], "get") == 0) {
        if (argc < 3) {
            rt_kprintf("Usage: qrcode_demo get <key>\n");
            return -1;
        }
        
        result = qrcode_data_get_string(argv[2], buffer, sizeof(buffer));
        if (result == RT_EOK) {
            rt_kprintf("%s = %s\n", argv[2], buffer);
        } else {
            rt_kprintf("Key '%s' not found or error: %d\n", argv[2], result);
        }
    }
    else if (strcmp(argv[1], "del") == 0) {
        if (argc < 3) {
            rt_kprintf("Usage: qrcode_demo del <key>\n");
            return -1;
        }
        
        result = qrcode_data_delete_string(argv[2]);
        if (result == RT_EOK) {
            rt_kprintf("Deleted key: %s\n", argv[2]);
#ifdef PKG_USING_LVGL
            auto_refresh_qrcode();
#endif
        } else {
            rt_kprintf("Failed to delete %s: %d\n", argv[2], result);
        }
    }
    else if (strcmp(argv[1], "device") == 0) {
        if (argc < 4) {
            rt_kprintf("Usage: qrcode_demo device <id> <sn> [model] [version]\n");
            return -1;
        }
        
        const char *model = (argc > 4) ? argv[4] : NULL;
        const char *version = (argc > 5) ? argv[5] : NULL;
        
        result = qrcode_data_save_device_info(argv[2], argv[3], model, version);
        if (result == RT_EOK) {
            rt_kprintf("Device info saved: ID=%s, SN=%s", argv[2], argv[3]);
            if (model) rt_kprintf(", Model=%s", model);
            if (version) rt_kprintf(", Version=%s", version);
            rt_kprintf("\n");
#ifdef PKG_USING_LVGL
            auto_refresh_qrcode();
#endif
        } else {
            rt_kprintf("Failed to save device info: %d\n", result);
        }
    }
    else if (strcmp(argv[1], "wifi") == 0) {
        if (argc < 4) {
            rt_kprintf("Usage: qrcode_demo wifi <ssid> <password>\n");
            return -1;
        }
        
        result = qrcode_data_save_wifi_info(argv[2], argv[3]);
        if (result == RT_EOK) {
            rt_kprintf("WiFi info saved: SSID=%s, Password=%s\n", argv[2], argv[3]);
#ifdef PKG_USING_LVGL
            auto_refresh_qrcode();
#endif
        } else {
            rt_kprintf("Failed to save WiFi info: %d\n", result);
        }
    }
    else if (strcmp(argv[1], "generate") == 0) {
        char qr_string[QRCODE_JSON_BUFFER_SIZE];
        
        result = qrcode_data_generate_qr_string(qr_string, sizeof(qr_string));
        if (result == RT_EOK) {
            rt_kprintf("QR String: %s\n", qr_string);
        } else {
            rt_kprintf("Failed to generate QR string: %d\n", result);
        }
    }
    else if (strcmp(argv[1], "refresh") == 0) {
#ifdef PKG_USING_LVGL
        auto_refresh_qrcode();
        rt_kprintf("QR code refreshed\n");
#else
        rt_kprintf("LVGL not enabled, cannot refresh UI\n");
#endif
    }
    else if (strcmp(argv[1], "status") == 0) {
        rt_kprintf("QR Data Manager Status:\n");
        rt_kprintf("  Initialized: %s\n", qrcode_data_is_initialized() ? "Yes" : "No");
        rt_kprintf("  MTD Device: %s\n", QRCODE_MTD_DEVICE_NAME);
        rt_kprintf("  Data Offset: 0x%x\n", QRCODE_DATA_OFFSET);
        rt_kprintf("  Data Size: %d bytes\n", QRCODE_DATA_SIZE);
        
        /* Show some key values */
        const char *keys[] = {
            QRCODE_KEY_DEVICE_ID,
            QRCODE_KEY_DEVICE_SN,
            QRCODE_KEY_WIFI_SSID,
            QRCODE_KEY_SERVER_URL
        };
        
        rt_kprintf("  Stored Data:\n");
        for (int i = 0; i < sizeof(keys) / sizeof(keys[0]); i++) {
            if (qrcode_data_get_string(keys[i], buffer, sizeof(buffer)) == RT_EOK) {
                rt_kprintf("    %s = %s\n", keys[i], buffer);
            }
        }
    }
    else {
        rt_kprintf("Unknown command: %s\n", argv[1]);
        return -1;
    }
    
    return 0;
}

/* Register command */
MSH_CMD_EXPORT_ALIAS(qrcode_demo_cmd, qrcode_demo, QR code data manager demo);

/**
 * @brief Auto-initialize QR data manager on startup
 * This function combines initialization and demo setup functionality
 */
static int qrcode_auto_init(void)
{
    rt_err_t result;
    char temp_buffer[64];
    
    /* Auto initialize */
    result = qrcode_data_manager_init();
    if (result == RT_EOK) {
        rt_kprintf("QR data manager auto-initialized\n");
        
        /* Check if device ID already exists, if not, set default values */
        result = qrcode_data_get_string(QRCODE_KEY_DEVICE_ID, temp_buffer, sizeof(temp_buffer));
        if (result != RT_EOK) {
            /* No existing data found, set default values */
            rt_kprintf("No existing QR data found, setting default values\n");
            qrcode_data_save_device_info("DW-2025-001", "SN123456789", "D12-HMI-Dishwasher", "v2.1.0");
            qrcode_data_save_wifi_info("DefaultWiFi", "default123456");
            qrcode_data_save_string(QRCODE_KEY_SERVER_URL, "https://api.example.com");
            qrcode_data_save_string(QRCODE_KEY_CUSTOM_INFO, "Default Mode");
        } else {
            rt_kprintf("Existing QR data found, keeping current values\n");
        }
        
#ifdef PKG_USING_LVGL
        /* Delay a bit for UI to be ready, then refresh */
        rt_thread_mdelay(1000);
        auto_refresh_qrcode();
#endif
    } else {
        rt_kprintf("Failed to auto-initialize QR data manager: %d\n", result);
    }
    
    return 0;
}
INIT_APP_EXPORT(qrcode_auto_init);